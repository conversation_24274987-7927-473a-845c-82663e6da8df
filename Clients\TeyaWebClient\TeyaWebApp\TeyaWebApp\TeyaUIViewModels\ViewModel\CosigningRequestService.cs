using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using DotNetEnv;

namespace TeyaUIViewModels.ViewModel
{
    public class CosigningRequestService : ICosigningRequestService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _encounterNotesURL;
        private readonly ITokenService _tokenService;
        private readonly ILogger<CosigningRequestService> _logger;

        public CosigningRequestService(
            HttpClient httpClient, 
            IStringLocalizer<TeyaUIViewModelsStrings> localizer, 
            ITokenService tokenService,
            ILogger<CosigningRequestService> logger)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _tokenService = tokenService;
            _logger = logger;
            Env.Load();
            _encounterNotesURL = Environment.GetEnvironmentVariable("EncounterNotesURL");
        }

        public async Task<Guid> CreateRequestAsync(CosigningRequest request, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigning/request-cosigning/{request.RecordId}/{request.RequesterId}/{request.RequesterName}/{request.ReviewerId}/{request.ReviewerName}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<dynamic>(responseContent);
                    _logger.LogInformation("Successfully created cosigning request for record {RecordId}", request.RecordId);
                    return request.Id; // Return the request ID
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to create cosigning request for record {RecordId}. Status: {StatusCode}, Error: {Error}", request.RecordId, response.StatusCode, errorContent);
                    throw new HttpRequestException($"Failed to create cosigning request: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating cosigning request for record {RecordId}", request.RecordId);
                throw;
            }
        }

        public async Task ApproveRequestAsync(Guid requestId, Guid reviewerId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/approve/{requestId}/{reviewerId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to approve cosigning request {RequestId}. Status: {StatusCode}, Error: {Error}", requestId, response.StatusCode, errorContent);
                    throw new HttpRequestException($"Failed to approve cosigning request: {errorContent}");
                }

                _logger.LogInformation("Successfully approved cosigning request {RequestId}", requestId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving cosigning request {RequestId}", requestId);
                throw;
            }
        }

        public async Task AddCommentAsync(Guid requestId, Guid commenterId, string commenterName, string comment, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/comment/{requestId}/{commenterId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                
                // Add comment as request body
                requestMessage.Content = new StringContent(JsonSerializer.Serialize(comment), Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to add comment to cosigning request {RequestId}. Status: {StatusCode}, Error: {Error}", requestId, response.StatusCode, errorContent);
                    throw new HttpRequestException($"Failed to add comment: {errorContent}");
                }

                _logger.LogInformation("Successfully added comment to cosigning request {RequestId} by {CommenterName}", requestId, commenterName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding comment to cosigning request {RequestId}", requestId);
                throw;
            }
        }

        public async Task ResolveCommentAsync(Guid requestId, Guid commentId, Guid resolvedById, string resolvedByName, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/resolve-comment/{requestId}/{commentId}/{resolvedById}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to resolve comment {CommentId} in cosigning request {RequestId}. Status: {StatusCode}, Error: {Error}", commentId, requestId, response.StatusCode, errorContent);
                    throw new HttpRequestException($"Failed to resolve comment: {errorContent}");
                }

                _logger.LogInformation("Successfully resolved comment {CommentId} in cosigning request {RequestId} by {ResolvedByName}", commentId, requestId, resolvedByName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resolving comment {CommentId} in cosigning request {RequestId}", commentId, requestId);
                throw;
            }
        }

        public async Task<CosigningRequest> GetByIdAsync(Guid requestId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/{requestId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var request = await response.Content.ReadFromJsonAsync<CosigningRequest>();
                    _logger.LogInformation("Retrieved cosigning request {RequestId}", requestId);
                    return request;
                }
                else
                {
                    _logger.LogWarning("Failed to get cosigning request {RequestId}. Status: {StatusCode}", requestId, response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cosigning request {RequestId}", requestId);
                throw;
            }
        }

        public async Task<CosigningRequest> GetByRecordIdAsync(Guid recordId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/by-record/{recordId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);

                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var request = await response.Content.ReadFromJsonAsync<CosigningRequest>();
                    _logger.LogInformation("Retrieved cosigning request by recordId {RecordId}", recordId);
                    return request;
                }
                else
                {
                    _logger.LogWarning("Failed to get cosigning request by recordId {RecordId}. Status: {StatusCode}", recordId, response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cosigning request by recordId {RecordId}", recordId);
                throw;
            }
        }


        public async Task<IEnumerable<CosigningComment>> GetCommentsAsync(Guid requestId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/comments/{requestId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var comments = await response.Content.ReadFromJsonAsync<List<CosigningComment>>();
                    _logger.LogInformation("Retrieved {Count} comments for cosigning request {RequestId}", comments?.Count ?? 0, requestId);
                    return comments ?? new List<CosigningComment>();
                }
                else
                {
                    _logger.LogWarning("Failed to get comments for cosigning request {RequestId}. Status: {StatusCode}", requestId, response.StatusCode);
                    return new List<CosigningComment>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting comments for cosigning request {RequestId}", requestId);
                throw;
            }
        }

        public async Task<IEnumerable<CosigningRequest>> GetPendingRequestsAsync(Guid reviewerId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/pending/{reviewerId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var requests = await response.Content.ReadFromJsonAsync<List<CosigningRequest>>();
                    _logger.LogInformation("Retrieved {Count} pending requests for reviewer {ReviewerId}", requests?.Count ?? 0, reviewerId);
                    return requests ?? new List<CosigningRequest>();
                }
                else
                {
                    _logger.LogWarning("Failed to get pending requests for reviewer {ReviewerId}. Status: {StatusCode}", reviewerId, response.StatusCode);
                    return new List<CosigningRequest>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending requests for reviewer {ReviewerId}", reviewerId);
                throw;
            }
        }

        public async Task<IEnumerable<CosigningRequest>> GetChangesRequestedAsync(Guid requesterId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/changes-requested/{requesterId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var requests = await response.Content.ReadFromJsonAsync<List<CosigningRequest>>();
                    _logger.LogInformation("Retrieved {Count} change-requested requests for requester {RequesterId}", requests?.Count ?? 0, requesterId);
                    return requests ?? new List<CosigningRequest>();
                }
                else
                {
                    _logger.LogWarning("Failed to get change-requested requests for requester {RequesterId}. Status: {StatusCode}", requesterId, response.StatusCode);
                    return new List<CosigningRequest>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting change-requested requests for requester {RequesterId}", requesterId);
                throw;
            }
        }

        public async Task<IEnumerable<CosigningRequest>> GetByReviewerIdAsync(Guid reviewerId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/pending/{reviewerId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var requests = await response.Content.ReadFromJsonAsync<List<CosigningRequest>>();
                    _logger.LogInformation("Retrieved {Count} requests for reviewer {ReviewerId}", requests?.Count ?? 0, reviewerId);
                    return requests ?? new List<CosigningRequest>();
                }
                else
                {
                    _logger.LogWarning("Failed to get requests for reviewer {ReviewerId}. Status: {StatusCode}", reviewerId, response.StatusCode);
                    return new List<CosigningRequest>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting requests for reviewer {ReviewerId}", reviewerId);
                return new List<CosigningRequest>();
            }
        }

        public async Task<CosigningRequest> GetActiveRequestAsync(Guid recordId, Guid organizationId, bool subscription)
        {
            try
            {
                // Use the cosigning request status endpoint
                var apiUrl = $"{_encounterNotesURL}/api/cosigning/request-status/{recordId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var request = await response.Content.ReadFromJsonAsync<CosigningRequest>();
                    _logger.LogInformation("Retrieved active request for record {RecordId}", recordId);
                    return request;
                }
                else
                {
                    _logger.LogWarning("No active request found for record {RecordId}. Status: {StatusCode}", recordId, response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active request for record {RecordId}", recordId);
                throw;
            }
        }

        public async Task CancelRequestAsync(Guid requestId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/{requestId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to cancel cosigning request {RequestId}. Status: {StatusCode}, Error: {Error}", requestId, response.StatusCode, errorContent);
                    throw new HttpRequestException($"Failed to cancel cosigning request: {errorContent}");
                }

                _logger.LogInformation("Successfully cancelled cosigning request {RequestId}", requestId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error canceling cosigning request {RequestId}", requestId);
                throw;
            }
        }

        public async Task<IEnumerable<CosigningRequest>> GetByRequesterIdAsync(Guid requesterId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/requester/{requesterId}?organizationId={organizationId}&subscription={subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var requests = await response.Content.ReadFromJsonAsync<IEnumerable<CosigningRequest>>();
                    _logger.LogInformation("Retrieved {Count} requests for requester {RequesterId}", requests?.Count() ?? 0, requesterId);
                    return requests ?? new List<CosigningRequest>();
                }
                _logger.LogWarning("Failed to get requests by requester ID: {StatusCode}", response.StatusCode);
                return new List<CosigningRequest>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting requests by requester ID: {RequesterId}", requesterId);
                return new List<CosigningRequest>();
            }
        }

        public async Task<int> GetPendingRequestCountAsync(Guid reviewerId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigningrequest/reviewer/{reviewerId}/pending-count?organizationId={organizationId}&subscription={subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var count = await response.Content.ReadFromJsonAsync<int>();
                    return count;
                }
                _logger.LogWarning("Failed to get pending request count: {StatusCode}", response.StatusCode);
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending request count for reviewer: {ReviewerId}", reviewerId);
                return 0;
            }
        }


    }
}
