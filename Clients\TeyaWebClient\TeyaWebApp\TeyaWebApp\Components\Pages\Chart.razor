﻿@page "/Chart"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "ChartAccessPolicy")]
@inject NavigationManager Navigation
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@layout Admin
@inject TeyaUIViewModels.ViewModel.IChartService ChartService
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject NavigationManager NavigationManager
@using Microsoft.AspNetCore.WebUtilities


@if (@PatientData.Name != null)
{
    <div class="d-flex gap-1 full-height py-1">
        <!-- Left Side - Patient Dashboard -->
        <MudItem xs="3" Class="patient-info-container">
            <MudPaper Elevation="1" Class="pa-4 h-100 d-flex flex-column justify-between fixed-dashboard dashboard-content">
                <!-- Patient Image with default fallback -->
                <div class="d-flex justify-content-center">
                    @if (!string.IsNullOrEmpty(PatientData.PatientImageURL))
                    {
                        <img src="@PatientData.PatientImageURL" alt="@Localizer["Patient Image"]" class="mb-3 patient-avatar rounded-circle" style="width: 80px; height: 80px; object-fit: cover;" />
                    }
                    else
                    {
                        <div class="default-avatar mb-3 rounded-circle d-flex justify-content-center align-items-center">
                            <svg class="default-person-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                    }
                </div>

                <!-- Patient Details -->
                <MudTypography Variant="h4" Class="mb-2 font-weight-bold text-center patient-name">
                    <h5>@PatientData.Name</h5>
                </MudTypography>
                <MudTypography Variant="h6" Class="text-left detail-item"><strong>@Localizer["Age"]:</strong> @(DateTime.TryParse(PatientData.DOB.ToString(), out DateTime dob) ? CalculateAge(dob) : PatientData.DOB)</MudTypography>
                <MudTypography Variant="h6" Class="text-left detail-item"><strong>@Localizer["Gender"]:</strong> @PatientData.Sex</MudTypography>
                <MudTypography Variant="h6" Class="text-left detail-item"><strong>@Localizer["Phone"]:</strong> @PatientData.PhoneNumber</MudTypography>
                <MudTypography Variant="h6" Class="text-left mb-2 detail-item"> <strong>@Localizer["Email"]:</strong> @PatientData.Email</MudTypography>

                <div class="section-divider"></div>

                <!-- Address -->
                <MudTypography Variant="h5" Class="mb-2 section-header">@Localizer["Address"]</MudTypography>
                <MudTypography Variant="body1" Class="info-text">@PatientData.Street</MudTypography>
                <MudTypography Variant="body1" Class="info-text">@PatientData.City</MudTypography>
                <MudTypography Variant="body1" Class="mb-2 info-text">@PatientData.State  @PatientData.PostalCode</MudTypography>

                <div class="section-divider"></div>

                <!-- Insurance Details -->
                <MudTypography Variant="h5" Class="mb-2 section-header">@Localizer["Insurance"]</MudTypography>
                <MudTypography Variant="body1" Class="info-text"><strong>@Localizer["Provider"]:</strong> @PatientData.PrimaryInsuranceProvider</MudTypography>
                <MudTypography Variant="body1" Class="info-text"><strong>@Localizer["Policy"]:</strong> @PatientData.PolicyNumber</MudTypography>
                <MudTypography Variant="body1" Class="info-text"><strong>@Localizer["Plan Name"]:</strong> @PatientData.PlanName</MudTypography>
                <MudTypography Variant="body1" Class="mb-2 info-text"><strong>@Localizer["Group Number"]:</strong> @PatientData.GroupNumber</MudTypography>

                <div class="section-divider"></div>

                <!-- Additional Medical Information -->
                <MudTypography Variant="h5" Class="mb-2 section-header">@Localizer["Medical Information"]</MudTypography>
                <MudTypography Variant="body1" Class="info-text"><strong>@Localizer["Primary Care Physician"]:</strong> @PatientData.PCPName</MudTypography>
                <MudTypography Variant="body1" Class="info-text"><strong>@Localizer["Allergies"]:</strong> @Localizer["Soy, Peanuts"]</MudTypography>
            </MudPaper>
        </MudItem>

        <!-- Right Side - Tabs Section with added left margin to account for fixed panel -->
        <MudItem xs="9" Class="d-flex flex-column main-content">
            <MudPaper Elevation="1" Class="mb-3">
                <MudTabs @bind-ActivePanelIndex="activeTabIndex" @bind-ActivePanelIndex:after="OnTabChanged" Elevation="0" Color="Color.Primary" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-3" Class="custom-tabs">
                    <MudTabPanel Text="@Localizer["Medical Summary"]" Class="@GetTabClass(0)" Style="margin-left: 20px;"><NotesSummary></NotesSummary></MudTabPanel>
                    <MudTabPanel Text="@Localizer["Notes"]" Class="@GetTabClass(1)">
                        <Notes SelectedNoteId="@selectedNoteId" OnGoBackToEncounters="HandleGoBackToEncounters" OnClearSelectedNoteId="ClearSelectedNoteId" ShowSingleRecordView="@showSingleRecordView" />
                    </MudTabPanel>
                    <MudTabPanel Text="@Localizer["Encounters"]" Class="@GetTabClass(2)">
                        <Encounters OnRowClick="SwitchToNotesTab" />
                    </MudTabPanel>
                    <MudTabPanel Text="@Localizer["CDSS"]" Class="@GetTabClass(3)">
                        <Alerts OnSwitchToNotes="SwitchTab" />
                    </MudTabPanel>
                </MudTabs>
            </MudPaper>
        </MudItem>
    </div>
}
else
{
    <MudItem Class="d-flex flex-column">
        <MudPaper Elevation="1">
            <MudTabs @bind-ActivePanelIndex="activeTabIndex" @bind-ActivePanelIndex:after="OnTabChanged" Elevation="0" Color="Color.Primary" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-3" Class="custom-tabs">
                <MudTabPanel Text="@Localizer["Notes"]" Class="@GetTabClass(0)">
                    <Notes SelectedNoteId="@selectedNoteId" OnGoBackToEncounters="HandleGoBackToEncounters" OnClearSelectedNoteId="ClearSelectedNoteId" ShowSingleRecordView="@showSingleRecordView" />
                </MudTabPanel>
                <MudTabPanel Text="@Localizer["Encounters"]" Class="@GetTabClass(1)">
                    <Encounters OnRowClick="SwitchToNotesTab" />
                </MudTabPanel>
                <MudTabPanel Text="@Localizer["CDSS"]" Class="@GetTabClass(2)">
                    <Alerts OnSwitchToNotes="SwitchTab" />
                </MudTabPanel>
                <MudTabPanel Text="@Localizer["Review"]" Class="@GetTabClass(2)">
                    <ReviewSoap></ReviewSoap>
                </MudTabPanel>
            </MudTabs>
        </MudPaper>
    </MudItem>
}

<style>
    /* Dashboard content styling */
    .dashboard-content {
        font-family: 'Atlassian Text', Arial, sans-serif;
        line-height: 1.3; /* Reduced from 1.5 */
    }

    .fixed-dashboard {
        position: fixed;
        top: 65px; /* Change this value to adjust the gap */
        width: 22%;
        max-height: 85vh;
        overflow-y: auto;
        z-index: 100;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-radius: 8px;
    }

    /* Main content area adjustment for reduced gap */
    .main-content {
        margin-left: 23%;
        width: 77%;
    }

    /* Custom tabs styling */
    .custom-tabs .mud-tabs-toolbar {
        padding-left: 20px;
    }

    /* Tab active state styling */
    .tab-active {
        color: white !important;
        background-color: var(--mud-palette-primary) !important;
    }

    /* Ensure all tabs have consistent styling when active */
    .mud-tab.mud-tab-active {
        color: white !important;
        background-color: var(--mud-palette-primary) !important;
    }

    /* Override any conflicting styles for CDSS tab */
    .mud-tab.mud-tab-active .mud-tab-content {
        color: white !important;
    }

    /* Section headers */
    .section-header {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0px; /* Reduced from 2px */
        margin-top: 0px; /* Reduced from 2px */
        letter-spacing: 0.2px;
    }
    
    /* Replace dividers with thin black lines */
    .section-divider {
        height: 1px;
        background-color: #000000;
        border-bottom: 1px solid #000000;
        width: 100%;
        margin: 10px 0;
    }
    
    /* Default avatar styling - similar to WhatsApp */
    .default-avatar {
        width: 80px;
        height: 80px;
        background-color: #DFE5E7;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .default-person-icon {
        width: 48px;
        height: 48px;
        fill: #6B767B;
    }
</style>
