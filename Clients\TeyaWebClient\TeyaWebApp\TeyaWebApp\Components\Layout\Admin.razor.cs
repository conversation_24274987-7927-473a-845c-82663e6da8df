﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.CognitiveServices.Speech.Transcription;
using Microsoft.Graph.Models;
using Microsoft.JSInterop;
using MudBlazor;
using Sprache;
using Syncfusion.Blazor;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using static System.Net.WebRequestMethods;
using static TeyaWebApp.Components.Layout.Admin;
namespace TeyaWebApp.Components.Layout
{
    public partial class Admin : IDisposable
    {
        private bool _isSearching = false;
        private bool _drawerOpen = true;
        private Member? _selectedMember;
        private List<Member> _members = new();
        private bool _isLoading = false;
        private Guid activeUser;
        private bool _isThemeLoaded = false;
        private string _selectedProductName = string.Empty;

        [Inject] private ActiveUser User { get; set; }
        [Inject] private IUserThemeService UserThemeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] public GraphApiService GraphApiService { get; set; }
        [Inject] public HttpClient Http { get; set; }
        [Inject] private IJSRuntime JSRuntime { get; set; }
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }
        [Inject] private NavigationManager Navigation { get; set; }

        private MudTheme CurrentTheme;
        private ThemeItem SelectedTheme;
        private List<UserTheme> UsersTheme;
        private List<ThemeItem> Themes = new();
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;

        private List<string> AvailableProducts = new();
        private bool HasMultipleProducts = false;
        private bool HasEHRAccess = false;

        // Cosigning request counts
        private int _incomingRequestCount = 0;
        private int _myRequestCount = 0;
        private Timer _requestCountTimer;
        private bool HasBillingAccess = false;
        private List<Product> AllProducts = new();
        private List<ProductOrganizationMapping> UserProductMappings = new();

        [Inject] private IProductService ProductService { get; set; }
        [Inject] private IProductOrganizationMappingService ProductOrganizationMappingService { get; set; }
        [Inject] private UserContext usercontext { get; set; }
        private Guid ActiveUserOrgID { get; set; }

        private async Task LoadProductsAndMappingsAsync()
        {
            try
            {
                AllProducts = await ProductService.GetProductsAsync() ?? new List<Product>();

                if (!string.IsNullOrEmpty(User.OrganizationName))
                {
                    var organizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                    if (organizationId != Guid.Empty)
                    {
                        var allMappings = await ProductOrganizationMappingService.GetAllProductOrganizationMappingsAsync();
                        UserProductMappings = allMappings?.Where(m => m.OrganizationId == organizationId && m.IsActive).ToList()
                                           ?? new List<ProductOrganizationMapping>();

                        await DetermineProductAccess();
                        await InitializeProductSwitcher();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading products and mappings: {ex.Message}");
            }
        }

        private async Task DetermineProductAccess()
        {
            try
            {
                var ehrProduct = AllProducts.FirstOrDefault(p => p.Name.Equals("EHR", StringComparison.OrdinalIgnoreCase));
                var billingProduct = AllProducts.FirstOrDefault(p => p.Name.Equals("Billing", StringComparison.OrdinalIgnoreCase));

                if (ehrProduct != null)
                {
                    HasEHRAccess = UserProductMappings.Any(m => m.ProductId == ehrProduct.Id);
                }

                if (billingProduct != null)
                {
                    HasBillingAccess = UserProductMappings.Any(m => m.ProductId == billingProduct.Id);
                }

                if (!HasEHRAccess && !HasBillingAccess)
                {
                    HasEHRAccess = true;
                    Logger.LogInformation("No product mappings found, defaulting to EHR access");
                }

                Logger.LogInformation($"Product Access - EHR: {HasEHRAccess}, Billing: {HasBillingAccess}");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error determining product access: {ex.Message}");
                HasEHRAccess = true;
            }
            finally
            {
                HasEHRAccess = true;
            }
        }

        private async Task InitializeProductSwitcher()
        {
            AvailableProducts.Clear();

            if (HasEHRAccess)
            {
                AvailableProducts.Add("EHR");
            }

            if (HasBillingAccess)
            {
                AvailableProducts.Add("Billing");
            }

            HasMultipleProducts = AvailableProducts.Count > 1;
        }

        public bool ShouldShowProductSwitcher() => HasMultipleProducts;
        public List<string> GetAvailableProducts() => AvailableProducts;

        private string GetProductIcon(string product)
        {
            return product switch
            {
                "EHR" => Icons.Material.Filled.LocalHospital,
                "Billing" => Icons.Material.Filled.Receipt,
                _ => Icons.Material.Filled.Apps
            };
        }

        private string GetProductDisplayName(string product)
        {
            return product switch
            {
                "EHR" => Localizer["EHRProduct"],
                "Billing" => Localizer["BillingProduct"],
                _ => product
            };
        }

        private async Task HandleProductSelection(string product)
        {
            var productUrl = GetProductUrl(product);
            if (!string.IsNullOrEmpty(productUrl))
            {
                var urlWithQuery = $"{Navigation.BaseUri.TrimEnd('/')}{productUrl}?product={product}";
                await JSRuntime.InvokeVoidAsync("open", urlWithQuery, "_blank");
            }
        }

        private string GetProductUrl(string product)
        {
            return product switch
            {
                "EHR" => "/Menu",
                "Billing" => "/ClaimsLookup",
                _ => string.Empty
            };
        }

        public void DrawerClose()
        {
            _drawerOpen = false;
        }
        private void DrawerToggle()
        {
            _drawerOpen = !_drawerOpen;
        }

        private void OpenProfileDialog()
        {
            var options = new DialogOptions
            {
                MaxWidth = MaxWidth.Medium,
                FullWidth = true,
                CloseOnEscapeKey = true,
                CloseButton = true,
            };
            DialogService.Show<ProfileDialog>(Localizer["Profile"], options);
        }

        private async Task<IEnumerable<Member>> SearchMembers(string searchTerm, CancellationToken cancellationToken)
        {
            IEnumerable<Member> result = Enumerable.Empty<Member>();
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                await Task.Delay(500, cancellationToken);
                try
                {

                    var matchingMembers = await MemberService.SearchMembersAsync(searchTerm, activeUserOrganizationId, Subscription);
                    matchingMembers = matchingMembers.Where(m => m.OrganizationID == ActiveUserOrgID).ToList();
                    result = matchingMembers;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, Localizer["ErrorSearchingMembers"]);
                    result = new List<Member>
                    {
                        new Member { Id = Guid.Empty, UserName = Localizer["AddMember"] }
                    };
                }
            }
            return result;
        }

        private async Task HandleKeyDown(KeyboardEventArgs e)
        {
            if (e.Key == "Enter")
            {
                await SearchAndNavigateAsync();
                _selectedMember = null;
            }
        }

        private async Task SearchAndNavigateAsync()
        {
            _isSearching = true;
            try
            {
                if (_selectedMember == null)
                {
                    Logger.LogInformation(Localizer["NoMemberSelected."]);
                    return;
                }

                if (_selectedMember.Id == Guid.Empty)
                {
                    var navigationPath = Localizer["PatientsPagePath"];
                    Navigation.NavigateTo(navigationPath);
                }
                else
                {
                    var navigationPathTemplate = Localizer["PatientsPathTemplate"];
                    var navigationPath = string.Format(navigationPathTemplate, _selectedMember.Id);
                    Navigation.NavigateTo(navigationPath);
                }
                _selectedMember = null;
            }
            finally
            {
                _isSearching = false;
            }
        }

        protected override async Task OnInitializedAsync()
        {
            ActiveUserOrgID = usercontext.ActiveUserOrganizationID;
            try
            {
                var uri = Navigation.ToAbsoluteUri(Navigation.Uri);
                if (QueryHelpers.ParseQuery(uri.Query).TryGetValue("product", out var productQuery))
                {
                    _selectedProductName = productQuery;
                }
                else
                {
                    _selectedProductName = "EHR";
                }

                var themesJson = await Http.GetStringAsync($"{Navigation.BaseUri}themes.json");
                var themes = JsonSerializer.Deserialize<Dictionary<string, ThemeModel>>(JsonDocument.Parse(themesJson).RootElement.GetProperty("themes").ToString());
                foreach (var themeData in themes)
                {
                    var themeModel = themeData.Value;
                    var mudTheme = MapToMudTheme(themeModel);
                    Themes.Add(new ThemeItem
                    {
                        Theme = MapToThemeModel(mudTheme),
                        Name = themeData.Key
                    });
                }

                await GraphApiService.GetLoggedInUserDetailsAsync();
                activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == "Enterprise";

                await LoadProductsAndMappingsAsync();
                UsersTheme = (await UserThemeService.GetUserThemesAsync(activeUserOrganizationId, Subscription)).ToList();

                if (string.IsNullOrEmpty(User?.id) || !Guid.TryParse(User.id, out Guid parsedUserId))
                {
                    SelectedTheme = Themes.FirstOrDefault();
                    CurrentTheme = MapToMudTheme(SelectedTheme.Theme);
                    _isThemeLoaded = true;
                    return;
                }

                activeUser = parsedUserId;
                var existingUserTheme = UsersTheme.FirstOrDefault(ut => activeUser == ut.UserId);
                SelectedTheme = Themes.FirstOrDefault(t => t.Name == existingUserTheme?.ThemeName);

                if (existingUserTheme == null)
                {
                    SelectedTheme = Themes.FirstOrDefault();
                    var newUserTheme = new UserTheme { UserId = activeUser, ThemeName = SelectedTheme.Name, OrganizationID = activeUserOrganizationId, Subscription = Subscription };
                    await UserThemeService.AddUserThemeAsync(newUserTheme);
                }

                CurrentTheme = MapToMudTheme(SelectedTheme.Theme);

                // Initialize cosigning request counts
                await LoadCosigningRequestCounts();

                // Subscribe to state changes - placeholder for future implementation

                // Set up timer to refresh counts every 30 seconds
                _requestCountTimer = new Timer(async _ => await LoadCosigningRequestCounts(), null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error initializing Admin layout: {ex.Message}");
            }
            finally
            {
                _isThemeLoaded = true;
                StateHasChanged();
            }
        }

        private MudTheme MapToMudTheme(ThemeModel themeModel)
        {
            return new MudTheme
            {
                PaletteLight = new PaletteLight
                {
                    Primary = themeModel.PaletteLight.Primary,
                    Secondary = themeModel.PaletteLight.Secondary,
                    AppbarBackground = themeModel.PaletteLight.AppbarBackground,
                    Background = themeModel.PaletteLight.Background,
                    Surface = themeModel.PaletteLight.Surface,
                    DrawerBackground = themeModel.PaletteLight.DrawerBackground,
                    TextPrimary = themeModel.PaletteLight.TextPrimary,
                    TextSecondary = themeModel.PaletteLight.TextSecondary,
                    ActionDefault = themeModel.PaletteLight.ActionDefault,
                    ActionDisabled = themeModel.PaletteLight.ActionDisabled,
                    ActionDisabledBackground = themeModel.PaletteLight.ActionDisabledBackground
                },
                PaletteDark = new PaletteDark
                {
                    Primary = themeModel.PaletteDark.Primary,
                    Secondary = themeModel.PaletteDark.Secondary,
                    AppbarBackground = themeModel.PaletteDark.AppbarBackground,
                    Background = themeModel.PaletteDark.Background,
                    Surface = themeModel.PaletteDark.Surface,
                    DrawerBackground = themeModel.PaletteDark.DrawerBackground,
                    TextPrimary = themeModel.PaletteDark.TextPrimary,
                    TextSecondary = themeModel.PaletteDark.TextSecondary,
                    ActionDefault = themeModel.PaletteDark.ActionDefault,
                    ActionDisabled = themeModel.PaletteDark.ActionDisabled,
                    ActionDisabledBackground = themeModel.PaletteDark.ActionDisabledBackground
                },
                LayoutProperties = new LayoutProperties
                {
                    DrawerWidthLeft = themeModel.LayoutProperties.DrawerWidthLeft,
                    DrawerWidthRight = themeModel.LayoutProperties.DrawerWidthRight,
                    AppbarHeight = themeModel.LayoutProperties.AppbarHeight
                }
            };
        }

        private ThemeModel MapToThemeModel(MudTheme mudTheme)
        {
            return new ThemeModel
            {
                PaletteLight = new ThemePalette
                {
                    Primary = mudTheme.PaletteLight.Primary.Value,
                    Secondary = mudTheme.PaletteLight.Secondary.Value,
                    AppbarBackground = mudTheme.PaletteLight.AppbarBackground.Value,
                    Background = mudTheme.PaletteLight.Background.Value,
                    Surface = mudTheme.PaletteLight.Surface.Value,
                    DrawerBackground = mudTheme.PaletteLight.DrawerBackground.Value,
                    TextPrimary = mudTheme.PaletteLight.TextPrimary.Value,
                    TextSecondary = mudTheme.PaletteLight.TextSecondary.Value,
                    ActionDefault = mudTheme.PaletteLight.ActionDefault.Value,
                    ActionDisabled = mudTheme.PaletteLight.ActionDisabled.Value,
                    ActionDisabledBackground = mudTheme.PaletteLight.ActionDisabledBackground.Value
                },
                PaletteDark = new ThemePalette
                {
                    Primary = mudTheme.PaletteDark.Primary.Value,
                    Secondary = mudTheme.PaletteDark.Secondary.Value,
                    AppbarBackground = mudTheme.PaletteDark.AppbarBackground.Value,
                    Background = mudTheme.PaletteDark.Background.Value,
                    Surface = mudTheme.PaletteDark.Surface.Value,
                    DrawerBackground = mudTheme.PaletteDark.DrawerBackground.Value,
                    TextPrimary = mudTheme.PaletteDark.TextPrimary.Value,
                    TextSecondary = mudTheme.PaletteDark.TextSecondary.Value,
                    ActionDefault = mudTheme.PaletteDark.ActionDefault.Value,
                    ActionDisabled = mudTheme.PaletteDark.ActionDisabled.Value,
                    ActionDisabledBackground = mudTheme.PaletteDark.ActionDisabledBackground.Value
                },
                LayoutProperties = new ThemeLayoutProperties
                {
                    DrawerWidthLeft = mudTheme.LayoutProperties.DrawerWidthLeft,
                    DrawerWidthRight = mudTheme.LayoutProperties.DrawerWidthRight,
                    AppbarHeight = mudTheme.LayoutProperties.AppbarHeight
                }
            };
        }

        private async Task LoadCosigningRequestCounts()
        {
            try
            {
                if (Guid.TryParse(User?.id, out var userId))
                {
                    // Load incoming requests (requests where current user is the reviewer)
                    _incomingRequestCount = await CosigningRequestService.GetPendingRequestCountAsync(userId,usercontext.ActiveUserOrganizationID,false);

                    // Load my requests (requests where current user is the requester)
                    var myRequests = await CosigningRequestService.GetByRequesterIdAsync(userId, usercontext.ActiveUserOrganizationID, false);
                    _myRequestCount = myRequests.Count(r => r.Status == CosigningRequestStatus.Pending || r.Status == CosigningRequestStatus.ChangesRequested);

                    await InvokeAsync(StateHasChanged);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading cosigning request counts: {ex.Message}");
            }
        }

        private void NavigateToReviewRequests()
        {
            Navigation.NavigateTo("/reviewrequests");
        }

        private void NavigateToMyRequests()
        {
            Navigation.NavigateTo("/myrequests");
        }

        private async void OnRequestCountsChanged()
        {
            await LoadCosigningRequestCounts();
        }

        public void Dispose()
        {
            _requestCountTimer?.Dispose();
            // Unsubscribe from state changes - placeholder for future implementation
        }
    }
}