using System;

namespace TeyaUIModels.Model
{
    public class CosigningRequest : IModel
    {
        public Guid Id { get; set; }
        public Guid RecordId { get; set; }
        public Guid RequesterId { get; set; }
        public Guid ReviewerId { get; set; }
        public string RequesterName { get; set; }
        public string ReviewerName { get; set; }
        public string PatientName { get; set; }
        public string PatientAge { get; set; }
        public string PatientGender { get; set; }
        public CosigningRequestStatus Status { get; set; } = CosigningRequestStatus.Pending;
        public string CommentsJson { get; set; }
        public DateTime RequestDate { get; set; }
        public DateTime? ReviewedDate { get; set; }
        public DateTime? LastUpdated { get; set; }
        public Guid OrganizationId { get; set; }
        public bool Subscription { get; set; }
        public bool IsDeleted { get; set; } = false;
    }

    public enum CosigningRequestStatus
    {
        Pending = 0,
        Approved = 1,
        ChangesRequested = 2
    }

    public class CosigningComment : IModel
    {
        public Guid Id { get; set; }
        public Guid RequestId { get; set; }
        public Guid CommenterId { get; set; }
        public string CommenterName { get; set; }
        public string Comment { get; set; }
        public string SelectedText { get; set; }
        public DateTime CommentDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsResolved { get; set; } = false;
        public DateTime? ResolvedDate { get; set; }
        public Guid? ResolvedById { get; set; }
        public string ResolvedByName { get; set; }
        public string CommentType { get; set; } = "Review";
        public Guid OrganizationId { get; set; }
        public bool Subscription { get; set; }
        public bool IsDeleted { get; set; } = false;
    }
}