using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICosigningRequestService
    {
        /// <summary>
        /// Create a new cosigning request
        /// </summary>
        Task<Guid> CreateRequestAsync(CosigningRequest request, Guid organizationId, bool subscription);

        /// <summary>
        /// Approve a cosigning request
        /// </summary>
        Task ApproveRequestAsync(Guid requestId, Guid reviewerId, Guid organizationId, bool subscription);

        /// <summary>
        /// Add a comment to a cosigning request (request changes)
        /// </summary>
        Task AddCommentAsync(Guid requestId, Guid commenterId, string commenterName, string comment, Guid organizationId, bool subscription);

        /// <summary>
        /// Resolve a comment in a cosigning request
        /// </summary>
        Task ResolveCommentAsync(Guid requestId, Guid commentId, Guid resolvedById, string resolvedByName, Guid organizationId, bool subscription);

        /// <summary>
        /// Get a cosigning request by ID
        /// </summary>
        Task<CosigningRequest> GetByIdAsync(Guid requestId, Guid organizationId, bool subscription);

        /// <summary>
        /// Get comments for a cosigning request
        /// </summary>
        Task<IEnumerable<CosigningComment>> GetCommentsAsync(Guid requestId, Guid organizationId, bool subscription);

        /// <summary>
        /// Get pending requests for a reviewer
        /// </summary>
        Task<IEnumerable<CosigningRequest>> GetPendingRequestsAsync(Guid reviewerId, Guid organizationId, bool subscription);

        /// <summary>
        /// Get requests that need changes for a requester
        /// </summary>
        Task<IEnumerable<CosigningRequest>> GetChangesRequestedAsync(Guid requesterId, Guid organizationId, bool subscription);

        /// <summary>
        /// Get requests by reviewer ID
        /// </summary>
        Task<IEnumerable<CosigningRequest>> GetByReviewerIdAsync(Guid reviewerId, Guid organizationId, bool subscription);

        /// <summary>
        /// Get active request for a record
        /// </summary>
        Task<CosigningRequest> GetActiveRequestAsync(Guid recordId, Guid organizationId, bool subscription);

        /// <summary>
        /// Cancel a cosigning request
        /// </summary>
        Task CancelRequestAsync(Guid requestId, Guid organizationId, bool subscription);

        /// <summary>
        /// Get requests by requester ID
        /// </summary>
        Task<IEnumerable<CosigningRequest>> GetByRequesterIdAsync(Guid requesterId, Guid organizationId, bool subscription);

        /// <summary>
        /// Get pending request count for a reviewer
        /// </summary>
        Task<int> GetPendingRequestCountAsync(Guid reviewerId, Guid organizationId, bool subscription);

        Task<CosigningRequest> GetByRecordIdAsync(Guid recordId, Guid organizationId, bool subscription);


    }
}
