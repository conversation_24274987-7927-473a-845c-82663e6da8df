using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ICosigningService
    {
        /// <summary>
        /// Sign a note
        /// </summary>
        Task SignNoteAsync(Guid recordId, Guid signerId, string signerName, Guid organizationId, bool subscription);

        /// <summary>
        /// Lock a note
        /// </summary>
        Task LockNoteAsync(Guid recordId, Guid signerId, string signerName, Guid organizationId, bool subscription);

        /// <summary>
        /// Request cosigning for a note
        /// </summary>
        Task RequestCosigningAsync(Guid recordId, Guid requesterId, string requesterName, Guid reviewerId, string reviewerName, Guid organizationId, bool subscription);

        /// <summary>
        /// Get cosigning status for a record
        /// </summary>
        Task<Cosigning> GetCosigningStatusAsync(Guid recordId, Guid organizationId, bool subscription);

        /// <summary>
        /// Get cosigning request status for a record
        /// </summary>
        Task<CosigningRequest> GetCosigningRequestStatusAsync(Guid recordId, Guid organizationId, bool subscription);
    }
}
