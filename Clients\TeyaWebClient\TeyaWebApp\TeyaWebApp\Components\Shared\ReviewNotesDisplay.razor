@using TeyaUIModels.Model
@using Microsoft.Extensions.Localization
@using Syncfusion.Blazor.RichTextEditor
@using System.Text.Json
@using System.Text.RegularExpressions

<style>
    /* Layout using CSS Grid - EXACT COPY from Notes page */
    .notes-layout-container {
        display: grid;
        gap: 1rem;
        width: 100%;
        max-width: 100%;
        overflow: hidden;
        align-items: start;
        box-sizing: border-box;
    }

    /* Always use no-transcription layout for review */
    .notes-layout-container.no-transcription {
        grid-template-columns: 1fr;
    }

    .notes-column {
        min-width: 0;
        overflow: hidden;
        width: 100%;
        box-sizing: border-box;
    }

    .notes-layout-container > * {
        min-width: 0;
    }

    /* Syncfusion RTE specific overrides (EXACT COPY from Notes page) */
    .e-richtexteditor {
        border: none !important;
        border-radius: 0 0 4px 4px !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }

    .e-richtexteditor .e-rte-content {
        border: none !important;
        min-height: 120px !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        box-sizing: border-box !important;
    }

    .e-richtexteditor .e-toolbar {
        border: none !important;
        border-bottom: 1px solid #ddd !important;
        background: #f8f9fa !important;
        box-sizing: border-box !important;
    }

    /* Section and subsection styling (EXACT COPY from Notes page) */
    .section-heading {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 16px;
        border-bottom: 2px solid #e3f2fd;
        padding-bottom: 8px;
    }

    .subsection-heading {
        font-size: 1rem;
        color: #333;
        font-weight: 500;
        margin-top: 12px;
        margin-bottom: 8px;
    }

    .description-container {
        margin-bottom: 16px;
    }

    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 12px;
        min-height: 60px;
        background: #fff;
        cursor: pointer;
        transition: border-color 0.2s;
    }

    .description-box:hover {
        border-color: #1976d2;
    }

    .description-box.empty {
        color: #999;
        font-style: italic;
    }

    .description-content {
        line-height: 1.5;
    }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        overflow: hidden;
    }

    /* Comment button styling */
    .comment-button {
        margin-left: 8px;
        padding: 4px 8px;
        font-size: 0.75rem;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .subsection-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 12px;
        margin-bottom: 8px;
    }
</style>

<div class="notes-layout-container no-transcription">
    <!-- Notes Column (without transcription) -->
    <div class="notes-column">
        @if (NotesData != null && NotesData.Any())
        {
            @foreach (var section in NotesData)
            {
                @foreach (var kvp in section)
                {
                    <MudCard Class="mt-3 pa-4">
                        <div class="section-header">
                            <p class="section-heading">
                                @kvp.Key
                            </p>
                            @if (ShowCommentButtons)
                            {
                                <MudButton Size="Size.Small" 
                                           Color="Color.Primary" 
                                           Variant="Variant.Outlined"
                                           StartIcon="@Icons.Material.Filled.Comment"
                                           Class="comment-button"
                                           OnClick="@(() => OnAddSectionComment?.Invoke(kvp.Key))">
                                    Comment
                                </MudButton>
                            }
                        </div>
                        
                        @foreach (var data in kvp.Value.Where(d => d.Key != "Transcription"))
                        {
                            <div class="subsection-header">
                                <p class="subsection-heading">
                                    @data.Key
                                </p>
                                @if (ShowCommentButtons)
                                {
                                    <MudButton Size="Size.Small" 
                                               Color="Color.Secondary" 
                                               Variant="Variant.Text"
                                               StartIcon="@Icons.Material.Filled.Comment"
                                               Class="comment-button"
                                               OnClick="@(() => OnAddSubsectionComment?.Invoke(kvp.Key, data.Key))">
                                        Comment
                                    </MudButton>
                                }
                            </div>

                            <div class="description-container">
                                @if (IsReadOnly)
                                {
                                    @{
                                        var content = GetEditorContent(Record, kvp.Key, data.Key);
                                        var displayContent = string.IsNullOrEmpty(content) ? "<div>No content...</div>" : content;
                                    }
                                    <div class="description-box @(string.IsNullOrEmpty(content) ? "empty" : "")">
                                        <div class="description-content">
                                            @((MarkupString)CleanHtml(displayContent))
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div class="editor-container">
                                        @{
                                            var editorKey = $"{Record.Id}{kvp.Key}{data.Key}";
                                            var content = GetEditorContent(Record, kvp.Key, data.Key);
                                            var editorContent = string.IsNullOrEmpty(content) ? "<div>Click to add notes...</div>" : content;
                                        }
                                        <SfRichTextEditor Value="@editorContent"
                                                          ValueChanged="@((string newValue) => OnContentChanged?.Invoke(kvp.Key, data.Key, newValue))"
                                                          Placeholder="@($"Enter {data.Key}...")">
                                            <RichTextEditorToolbarSettings Items="@GetToolbarItems()" />
                                        </SfRichTextEditor>
                                    </div>
                                }
                            </div>
                        }
                    </MudCard>
                }
            }
        }
        else
        {
            <MudAlert Severity="Severity.Info">
                No notes data available for review.
            </MudAlert>
        }
    </div>
</div>

@code {
    [Parameter] public Record Record { get; set; }
    [Parameter] public List<Dictionary<string, Dictionary<string, string>>> NotesData { get; set; }
    [Parameter] public bool IsReadOnly { get; set; } = true;
    [Parameter] public bool ShowCommentButtons { get; set; } = false;
    [Parameter] public EventCallback<(string sectionKey, string fieldKey, string newValue)> OnContentChanged { get; set; }
    [Parameter] public EventCallback<string> OnAddSectionComment { get; set; }
    [Parameter] public EventCallback<(string sectionKey, string subsectionKey)> OnAddSubsectionComment { get; set; }

    /// <summary>
    /// Get editor content for a specific field (similar to Notes page)
    /// </summary>
    private string GetEditorContent(Record record, string sectionKey, string fieldKey)
    {
        try
        {
            if (record?.Template == null) return string.Empty;

            var templateData = JsonSerializer.Deserialize<Dictionary<string, object>>(record.Template);
            if (templateData?.ContainsKey(fieldKey) == true)
            {
                return templateData[fieldKey]?.ToString() ?? string.Empty;
            }
            return string.Empty;
        }
        catch (Exception)
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// Clean HTML content for display
    /// </summary>
    private string CleanHtml(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return string.Empty;

        // Basic HTML cleaning - remove script tags and dangerous content
        content = Regex.Replace(content, @"<script[^>]*>.*?</script>", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
        content = Regex.Replace(content, @"<style[^>]*>.*?</style>", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);

        return content;
    }

    /// <summary>
    /// Get toolbar items for RichTextEditor
    /// </summary>
    private List<Syncfusion.Blazor.RichTextEditor.ToolbarItemModel> GetToolbarItems()
    {
        return new List<Syncfusion.Blazor.RichTextEditor.ToolbarItemModel>
        {
            new() { Name = "Bold" },
            new() { Name = "Italic" },
            new() { Name = "Underline" },
            new() { Name = "|" },
            new() { Name = "Formats" },
            new() { Name = "Alignments" },
            new() { Name = "|" },
            new() { Name = "OrderedList" },
            new() { Name = "UnorderedList" },
            new() { Name = "|" },
            new() { Name = "Undo" },
            new() { Name = "Redo" }
        };
    }
}
