@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging

<style>
    .signing-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 12px;
        padding: 24px;
        margin: 16px 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .signing-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .signature-display {
        background: white;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        min-height: 80px;
        position: relative;
    }

    .signature-display.signed {
        border-color: #4caf50;
        background: #f1f8e9;
    }

    .signature-display.locked {
        border-color: #ff9800;
        background: #fff3e0;
    }

    .signature-text {
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        line-height: 1.6;
        color: #333;
    }

    .action-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-top: 16px;
        border: 1px solid #e0e0e0;
    }

    .provider-selection {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 16px;
        margin: 12px 0;
        border: 1px solid #dee2e6;
    }

    .status-indicator {
        position: absolute;
        top: 8px;
        right: 8px;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-signed {
        background: #4caf50;
        color: white;
    }

    .status-locked {
        background: #ff9800;
        color: white;
    }

    .status-pending {
        background: #2196f3;
        color: white;
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        margin-top: 20px;
    }

    .request-status {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
    }

    .workflow-steps {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 20px 0;
        padding: 16px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        position: relative;
    }

    .step-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        font-weight: bold;
    }

    .step-active {
        background: #2196f3;
        color: white;
    }

    .step-completed {
        background: #4caf50;
        color: white;
    }

    .step-pending {
        background: #e0e0e0;
        color: #666;
    }

    .step-connector {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 100%;
        height: 2px;
        background: #e0e0e0;
        z-index: -1;
    }

    .step-connector.completed {
        background: #4caf50;
    }

    .professional-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
</style>

<div class="signing-container">
    <div class="professional-card">
        <!-- Header -->
        <div class="signing-header">
            <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                <MudIcon Icon="@Icons.Material.Filled.DriveFileRenameOutline" Size="Size.Large" />
                <div>
                    <MudText Typo="Typo.h6" Style="font-weight: 600; margin-bottom: 4px;">
                        @Localizer["DocumentSigning"]
                    </MudText>
                    <MudText Typo="Typo.body2" Style="opacity: 0.9;">
                        @PatientName • @Localizer["RecordId"]: @RecordId.ToString("N")[..8]
                    </MudText>
                </div>
            </MudStack>
        </div>

        <!-- Workflow Steps -->
        <div class="workflow-steps">
            <div class="step">
                <div class="step-icon @GetStepClass(1)">1</div>
                <MudText Typo="Typo.caption" Style="text-align: center;">@Localizer["Sign"]</MudText>
                @if (GetCurrentStep() > 1)
                {
                    <div class="step-connector completed"></div>
                }
            </div>
            <div class="step">
                <div class="step-icon @GetStepClass(2)">2</div>
                <MudText Typo="Typo.caption" Style="text-align: center;">@Localizer["RequestReview"]</MudText>
                @if (GetCurrentStep() > 2)
                {
                    <div class="step-connector completed"></div>
                }
            </div>
            <div class="step">
                <div class="step-icon @GetStepClass(3)">3</div>
                <MudText Typo="Typo.caption" Style="text-align: center;">@Localizer["Review"]</MudText>
                @if (GetCurrentStep() > 3)
                {
                    <div class="step-connector completed"></div>
                }
            </div>
            <div class="step">
                <div class="step-icon @GetStepClass(4)">4</div>
                <MudText Typo="Typo.caption" Style="text-align: center;">@Localizer["Lock"]</MudText>
            </div>
        </div>

        <!-- Signature Display -->
        <div class="signature-display @GetSignatureDisplayClass()">
            <div class="status-indicator @GetStatusIndicatorClass()">
                @GetStatusText()
            </div>
            
            @if (CurrentCosigning.IsLocked)
            {
                <MudIcon Icon="@Icons.Material.Filled.Lock" Color="Color.Warning" Size="Size.Large" Style="float: left; margin-right: 12px;" />
            }
            else if (CurrentCosigning.IsSigned)
            {
                <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Large" Style="float: left; margin-right: 12px;" />
            }
            else
            {
                <MudIcon Icon="@Icons.Material.Filled.PendingActions" Color="Color.Info" Size="Size.Large" Style="float: left; margin-right: 12px;" />
            }

            <div class="signature-text">
                @if (CurrentCosigning.IsLocked)
                {
                    <div>
                        <strong>@Localizer["DocumentLocked"]</strong><br />
                        @GetSignatureText()
                    </div>
                }
                else if (CurrentCosigning.IsSigned)
                {
                    <div>
                        @GetSignatureText()
                        @if (HasActiveRequest)
                        {
                            <br /><em>@Localizer["PendingCosigningReview"]</em>
                        }
                    </div>
                }
                else
                {
                    <div>
                        <em>@Localizer["DocumentNotSigned"]</em><br />
                        @Localizer["ClickSignToBegin"]
                    </div>
                }
            </div>
        </div>

        <!-- Active Request Status -->
        @if (HasActiveRequest && ActiveRequest != null)
        {
            <div class="request-status">
                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                    <MudIcon Icon="@Icons.Material.Filled.RateReview" Color="Color.Primary" />
                    <div>
                        <MudText Typo="Typo.subtitle2" Style="font-weight: 600;">
                            @Localizer["CosigningRequestActive"]
                        </MudText>
                        <MudText Typo="Typo.body2">
                            @Localizer["ReviewerName"]: @ActiveRequest.ReviewerName
                        </MudText>
                        <MudText Typo="Typo.caption" Style="color: #666;">
                            @Localizer["RequestedOn"]: @ActiveRequest.RequestDate.ToString("MMM dd, yyyy 'at' HH:mm")
                        </MudText>
                    </div>
                </MudStack>
            </div>
        }

        <!-- Action Section -->
        @if (!CurrentCosigning.IsLocked)
        {
            <div class="action-section">
                @if (!CurrentCosigning.IsSigned)
                {
                    <!-- Initial Signing -->
                    <MudText Typo="Typo.h6" Style="font-weight: 600; margin-bottom: 16px;">
                        @Localizer["SignDocument"]
                    </MudText>
                    <MudText Typo="Typo.body2" Style="margin-bottom: 16px; color: #666;">
                        @Localizer["SigningConfirmation"]
                    </MudText>
                    
                    <div class="action-buttons">
                        <MudButton Color="Color.Primary"
                                   Variant="Variant.Filled"
                                   Size="Size.Large"
                                   StartIcon="@Icons.Material.Filled.DriveFileRenameOutline"
                                   OnClick="SignDocument"
                                   Disabled="@IsProcessing">
                            @Localizer["SignDocument"]
                        </MudButton>
                    </div>
                }
                else if (!HasActiveRequest)
                {
                    <!-- Post-Signing Options -->
                    <MudText Typo="Typo.h6" Style="font-weight: 600; margin-bottom: 16px;">
                        @Localizer["NextSteps"]
                    </MudText>
                    
                    <MudRadioGroup @bind-Value="SelectedAction" Row="true" Class="mb-3">
                        <MudRadio Value="@SigningAction.Lock" Color="Color.Primary">
                            @Localizer["LockDocument"]
                        </MudRadio>
                        <MudRadio Value="@SigningAction.RequestCosigning" Color="Color.Primary">
                            @Localizer["RequestCosigning"]
                        </MudRadio>
                        <MudRadio Value="@SigningAction.SignAgain" Color="Color.Primary">
                            @Localizer["SignAgain"]
                        </MudRadio>
                    </MudRadioGroup>

                    @if (SelectedAction == SigningAction.RequestCosigning)
                    {
                        <div class="provider-selection">
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600; margin-bottom: 12px;">
                                @Localizer["SelectReviewer"]
                            </MudText>
                            <MudAutocomplete T="Member"
                                             @bind-Value="SelectedProvider"
                                             SearchFunc="SearchProviders"
                                             ToStringFunc="@(p => p?.UserName ?? "")"
                                             Label="@Localizer["SearchProviders"]"
                                             Variant="Variant.Outlined"
                                             AdornmentIcon="@Icons.Material.Filled.Search"
                                             AdornmentColor="Color.Primary" />
                            
                            @if (SelectedProvider != null)
                            {
                                <MudAlert Severity="Severity.Info" Dense="true" Class="mt-2">
                                    @Localizer["SelectedReviewer"]: <strong>@SelectedProvider.UserName</strong>
                                </MudAlert>
                            }
                        </div>
                    }

                    <div class="action-buttons">
                        @if (SelectedAction == SigningAction.Lock)
                        {
                            <MudButton Color="Color.Warning"
                                       Variant="Variant.Filled"
                                       Size="Size.Large"
                                       StartIcon="@Icons.Material.Filled.Lock"
                                       OnClick="LockDocument"
                                       Disabled="@IsProcessing">
                                @Localizer["LockDocument"]
                            </MudButton>
                        }
                        else if (SelectedAction == SigningAction.RequestCosigning)
                        {
                            <MudButton Color="Color.Secondary"
                                       Variant="Variant.Filled"
                                       Size="Size.Large"
                                       StartIcon="@Icons.Material.Filled.RateReview"
                                       OnClick="RequestCosigning"
                                       Disabled="@(IsProcessing || SelectedProvider == null)">
                                @Localizer["SendCosigningRequest"]
                            </MudButton>
                        }
                        else if (SelectedAction == SigningAction.SignAgain)
                        {
                            <MudButton Color="Color.Primary"
                                       Variant="Variant.Filled"
                                       Size="Size.Large"
                                       StartIcon="@Icons.Material.Filled.DriveFileRenameOutline"
                                       OnClick="SignDocument"
                                       Disabled="@IsProcessing">
                                @Localizer["SignAgain"]
                            </MudButton>
                        }
                    </div>
                }
                else
                {
                    <!-- Active Request Actions -->
                    <MudText Typo="Typo.h6" Style="font-weight: 600; margin-bottom: 16px;">
                        @Localizer["RequestPending"]
                    </MudText>
                    <MudText Typo="Typo.body2" Style="margin-bottom: 16px; color: #666;">
                        @Localizer["WaitingForReview"]
                    </MudText>
                    
                    <div class="action-buttons">
                        <MudButton Color="Color.Default"
                                   Variant="Variant.Outlined"
                                   Size="Size.Medium"
                                   StartIcon="@Icons.Material.Filled.Cancel"
                                   OnClick="CancelRequest"
                                   Disabled="@IsProcessing">
                            @Localizer["CancelRequest"]
                        </MudButton>
                    </div>
                }
            </div>
        }
    </div>
</div>

<!-- Loading Overlay -->
@if (IsProcessing)
{
    <MudOverlay Visible="true" DarkBackground="true" Absolute="false">
        <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
    </MudOverlay>
}

<!-- Success Dialog -->
<MudDialog @bind-IsVisible="ShowSuccessDialog" Options="@(new DialogOptions { CloseOnEscapeKey = true, MaxWidth = MaxWidth.Small })">
    <DialogContent>
        <MudStack AlignItems="AlignItems.Center" Spacing="3">
            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Large" />
            <MudText Typo="Typo.h6" Align="Align.Center">@SuccessMessage</MudText>
        </MudStack>
    </DialogContent>
    <DialogActions>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="@(() => ShowSuccessDialog = false)">
            @Localizer["OK"]
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    public enum SigningAction
    {
        Lock,
        RequestCosigning,
        SignAgain
    }
}
