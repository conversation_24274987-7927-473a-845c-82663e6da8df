﻿@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IDialogService DialogService
@inject NavigationManager NavigationManager
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@implements IDisposable

<head>
    <link href="../app.css" rel="stylesheet" />
</head>

<MudPaper Width="@(IsDrawerOpen ? "240px" : "60px")" Class="py-3" Elevation="0" Style="@(IsDrawerOpen ? "" : "padding-left: 0; padding-right: 0;")">
    <MudNavMenu>

        @if (HasEHRProductAccess() && (IsEHRSelected() || !HasBillingProductAccess()))
        {
            @if (IsPageAccessible(Chart))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/Chart" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["Chart"]
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["Chart"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment" 
                                           OnClick="@(() => NavigateToPage("/Chart"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }

            @if (IsPageAccessible(Appointments))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/Appointments" Icon="@Icons.Material.Filled.Event">
                        @Localizer["Appointments"]
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["Appointments"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Event" 
                                           OnClick="@(() => NavigateToPage("/Appointments"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }

            @if (IsPageAccessible(OrderSets))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/OrderSets" Icon="@Icons.Material.Filled.ListAlt">
                        @Localizer["OrderSets"]
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["OrderSets"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.ListAlt" 
                                           OnClick="@(() => NavigateToPage("/OrderSets"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }

            @if (IsPageAccessible(ReviewSoap))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/ReviewSoap" Icon="@GetReviewRequestIcon()">
                        <div class="d-flex align-center">
                            @Localizer["ReviewRequests"]
                            @if (PendingRequestCount > 0)
                            {
                                <MudChip T="string" Size="Size.Small" Color="Color.Error" Class="ml-2" Style="min-width: 20px; height: 20px; font-size: 0.75rem;">
                                    @PendingRequestCount
                                </MudChip>
                            }
                        </div>
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2 position-relative">
                        <MudTooltip Text="@GetReviewRequestTooltip()" Placement="Placement.Right">
                            <MudIconButton Icon="@GetReviewRequestIcon()"
                                           OnClick="@(() => NavigateToPage("/ReviewSoap"))"
                                           Color="@GetReviewRequestColor()"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        @if (PendingRequestCount > 0)
                        {
                            <MudChip T="string" Size="Size.Small" Color="Color.Error"
                                     Class="position-absolute"
                                     Style="top: -5px; right: -5px; min-width: 18px; height: 18px; font-size: 0.7rem; z-index: 1;">
                                @PendingRequestCount
                            </MudChip>
                        }
                    </div>
                }
            }

            @if (IsPageAccessible(ReviewRequests))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/reviewrequests" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["ReviewRequests"]
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["ReviewRequests"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment"
                                           OnClick="@(() => NavigateToPage("/reviewrequests"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }

            @if (IsPageAccessible(MyRequests))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/myrequests" Icon="@Icons.Material.Filled.PersonalVideo">
                        @Localizer["MyRequests"]
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["MyRequests"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.PersonalVideo"
                                           OnClick="@(() => NavigateToPage("/myrequests"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }

            @if (IsPageAccessible(CosigningDashboard))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/cosigning-dashboard" Icon="@Icons.Material.Filled.VerifiedUser">
                        @Localizer["CosigningDashboard"]
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["CosigningDashboard"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.VerifiedUser"
                                           OnClick="@(() => NavigateToPage("/cosigning-dashboard"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }

            @if (IsPageAccessible(Practice))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavGroup Class="mud-navgroup-style" Title="@Localizer["Practice"]" Expanded="true" Icon="@Icons.Material.Filled.Sports">
                        <MudNavLink Class="mud-navlink-style" Href="/OfficeVisit" Icon="@Icons.Material.Filled.People">
                            @Localizer["OfficeVisit"]
                        </MudNavLink>
                    </MudNavGroup>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["OfficeVisit"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.People" 
                                           OnClick="@(() => NavigateToPage("/OfficeVisit"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }

            @if (ShouldShowEHRSettings())
            {
                @if (IsDrawerOpen)
                {
                    <MudNavGroup Class="mud-navgroup-style" Title="@Localizer["Settings"]" Expanded="true" Icon="@Icons.Material.Filled.Settings">

                        @if (IsPageAccessible(Patients))
                        {
                            <MudNavLink Class="mud-navlink-style" Href="/Patients" Icon="@Icons.Material.Filled.People">
                                @Localizer["Patient"]
                            </MudNavLink>
                        }

                        @if (ShouldRenderLicenseLink)
                        {
                            @if (IsPageAccessible(LicenseActivation))
                            {
                                <MudNavGroup Class="mud-navgroup-style" Title="@Localizer["LicenseActivation"]" Expanded="true" Icon="@Icons.Material.Filled.Verified">
                                    @if (IsPageAccessible(License))
                                    {
                                        <MudNavLink Class="mud-navlink-style" Href="/License" Icon="@Icons.Material.Filled.Approval">
                                            @Localizer["License"]
                                        </MudNavLink>
                                    }
                                    @if (IsPageAccessible(ProductFeatureSettings))
                                    {
                                        <MudNavLink Class="mud-navlink-style" Href="/ProductFeatureSettings" Icon="@Icons.Material.Filled.ProductionQuantityLimits">
                                            @Localizer["ProductFeatures"]
                                        </MudNavLink>
                                    }
                                </MudNavGroup>
                            }
                            @if (IsPageAccessible(Security))
                            {
                                <MudNavLink Class="mud-navlink-style" Href="/Security" Icon="@Icons.Material.Filled.VerifiedUser">
                                    @Localizer["Security"]
                                </MudNavLink>
                            }
                        }

                        @if (IsPageAccessible(UserManagement))
                        {
                            <MudNavLink Class="mud-navlink-style" Href="/UserManagement" Icon="@Icons.Material.Filled.VerifiedUser">
                                @Localizer["UserManagement"]
                            </MudNavLink>
                        }

                        @if (IsPageAccessible(Templates))
                        {
                            <MudNavLink Class="mud-navlink-style" Href="/Templates" Icon="@Icons.Material.Filled.DocumentScanner">
                                @Localizer["Templates"]
                            </MudNavLink>
                        }
                        
                        @if (IsPageAccessible(Config))
                        {
                            <MudNavLink Class="mud-navlink-style" Href="/Config" Icon="@Icons.Material.Filled.AdminPanelSettings">
                                @Localizer["Config"]
                            </MudNavLink>
                        }
                        
                    </MudNavGroup>
                }
                else
                {
                    <!-- When collapsed, show individual icons for settings items -->
                    @if (IsPageAccessible(Patients))
                    {
                        <div class="d-flex justify-center ma-2">
                            <MudTooltip Text="@Localizer["Patient"]" Placement="Placement.Right">
                                <MudIconButton Icon="@Icons.Material.Filled.People" 
                                               OnClick="@(() => NavigateToPage("/Patients"))"
                                               Color="Color.Inherit"
                                               Size="Size.Medium" />
                            </MudTooltip>
                        </div>
                    }

                    @if (ShouldRenderLicenseLink)
                    {
                        @if (IsPageAccessible(License))
                        {
                            <div class="d-flex justify-center ma-2">
                                <MudTooltip Text="@Localizer["License"]" Placement="Placement.Right">
                                    <MudIconButton Icon="@Icons.Material.Filled.Approval" 
                                                   OnClick="@(() => NavigateToPage("/License"))"
                                                   Color="Color.Inherit"
                                                   Size="Size.Medium" />
                                </MudTooltip>
                            </div>
                        }
                        @if (IsPageAccessible(ProductFeatureSettings))
                        {
                            <div class="d-flex justify-center ma-2">
                                <MudTooltip Text="@Localizer["ProductFeatures"]" Placement="Placement.Right">
                                    <MudIconButton Icon="@Icons.Material.Filled.ProductionQuantityLimits" 
                                                   OnClick="@(() => NavigateToPage("/ProductFeatureSettings"))"
                                                   Color="Color.Inherit"
                                                   Size="Size.Medium" />
                                </MudTooltip>
                            </div>
                        }
                        @if (IsPageAccessible(Security))
                        {
                            <div class="d-flex justify-center ma-2">
                                <MudTooltip Text="@Localizer["Security"]" Placement="Placement.Right">
                                    <MudIconButton Icon="@Icons.Material.Filled.VerifiedUser" 
                                                   OnClick="@(() => NavigateToPage("/Security"))"
                                                   Color="Color.Inherit"
                                                   Size="Size.Medium" />
                                </MudTooltip>
                            </div>
                        }
                    }

                    @if (IsPageAccessible(UserManagement))
                    {
                        <div class="d-flex justify-center ma-2">
                            <MudTooltip Text="@Localizer["UserManagement"]" Placement="Placement.Right">
                                <MudIconButton Icon="@Icons.Material.Filled.VerifiedUser" 
                                               OnClick="@(() => NavigateToPage("/UserManagement"))"
                                               Color="Color.Inherit"
                                               Size="Size.Medium" />
                            </MudTooltip>
                        </div>
                    }

                    @if (IsPageAccessible(Templates))
                    {
                        <div class="d-flex justify-center ma-2">
                            <MudTooltip Text="@Localizer["Templates"]" Placement="Placement.Right">
                                <MudIconButton Icon="@Icons.Material.Filled.DocumentScanner" 
                                               OnClick="@(() => NavigateToPage("/Templates"))"
                                               Color="Color.Inherit"
                                               Size="Size.Medium" />
                            </MudTooltip>
                        </div>
                    }
                    
                    @if (IsPageAccessible(Config))
                    {
                        <div class="d-flex justify-center ma-2">
                            <MudTooltip Text="@Localizer["Config"]" Placement="Placement.Right">
                                <MudIconButton Icon="@Icons.Material.Filled.AdminPanelSettings" 
                                               OnClick="@(() => NavigateToPage("/Config"))"
                                               Color="Color.Inherit"
                                               Size="Size.Medium" />
                            </MudTooltip>
                        </div>
                    }
                }
            }
        }
        
        @if (IsBillingSelected() && HasBillingProductAccess())
        {
            @if (IsPageAccessible(ClaimsLookup))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/ClaimsLookup" Icon="@Icons.Material.Filled.Search">
                        @Localizer["ClaimsLookup"]
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["ClaimsLookup"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Search" 
                                           OnClick="@(() => NavigateToPage("/ClaimsLookup"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }
            @if (IsPageAccessible(PatientDentalClaims))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/DentalClaims" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["DentalClaims"]
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["DentalClaims"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment" 
                                           OnClick="@(() => NavigateToPage("/DentalClaims"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }

            @if (IsPageAccessible(BillingEncounters))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/BillingEncounters" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["Encounters"]
                    </MudNavLink>
                }
                else
                {
                    <div class="d-flex justify-center ma-2">
                        <MudTooltip Text="@Localizer["Encounters"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment" 
                                           OnClick="@(() => NavigateToPage("/BillingEncounters"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                    </div>
                }
            }
        }

        @if (!_isLoading && !HasEHRProductAccess() && !HasBillingProductAccess() && IsDrawerOpen)
        {
            <MudAlert Severity="Severity.Warning" Class="ma-2">
                <AlertContent>
                    <MudText Typo="Typo.body2">
                        @Localizer["NoProductsAvailable"]
                    </MudText>
                    <MudText Typo="Typo.caption">
                        @Localizer["ContactAdministrator"]
                    </MudText>
                </AlertContent>
            </MudAlert>
        }
    </MudNavMenu>
</MudPaper>