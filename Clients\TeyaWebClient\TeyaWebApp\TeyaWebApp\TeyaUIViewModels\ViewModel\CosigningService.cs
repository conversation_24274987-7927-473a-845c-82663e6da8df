using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;
using DotNetEnv;

namespace TeyaUIViewModels.ViewModel
{
    public class CosigningService : ICosigningService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _encounterNotesURL;
        private readonly ITokenService _tokenService;
        private readonly ILogger<CosigningService> _logger;

        public CosigningService(
            HttpClient httpClient, 
            IStringLocalizer<TeyaUIViewModelsStrings> localizer, 
            ITokenService tokenService,
            ILogger<CosigningService> logger)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _tokenService = tokenService;
            _logger = logger;
            Env.Load();
            _encounterNotesURL = Environment.GetEnvironmentVariable("EncounterNotesURL");
        }

        public async Task SignNoteAsync(Guid recordId, Guid signerId, string signerName, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigning/sign/{recordId}/{signerId}/{signerName}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to sign note {RecordId}. Status: {StatusCode}, Error: {Error}", recordId, response.StatusCode, errorContent);
                    throw new HttpRequestException($"Failed to sign note: {errorContent}");
                }

                _logger.LogInformation("Successfully signed note {RecordId} by {SignerName}", recordId, signerName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error signing note {RecordId}", recordId);
                throw;
            }
        }

        public async Task LockNoteAsync(Guid recordId, Guid signerId, string signerName, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigning/lock/{recordId}/{signerId}/{signerName}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to lock note {RecordId}. Status: {StatusCode}, Error: {Error}", recordId, response.StatusCode, errorContent);
                    throw new HttpRequestException($"Failed to lock note: {errorContent}");
                }

                _logger.LogInformation("Successfully locked note {RecordId} by {SignerName}", recordId, signerName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error locking note {RecordId}", recordId);
                throw;
            }
        }

        public async Task RequestCosigningAsync(Guid recordId, Guid requesterId, string requesterName, Guid reviewerId, string reviewerName, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigning/request-cosigning/{recordId}/{requesterId}/{requesterName}/{reviewerId}/{reviewerName}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to request cosigning for note {RecordId}. Status: {StatusCode}, Error: {Error}", recordId, response.StatusCode, errorContent);
                    throw new HttpRequestException($"Failed to request cosigning: {errorContent}");
                }

                _logger.LogInformation("Successfully requested cosigning for note {RecordId} from {RequesterName} to {ReviewerName}", recordId, requesterName, reviewerName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error requesting cosigning for note {RecordId}", recordId);
                throw;
            }
        }

        public async Task<Cosigning> GetCosigningStatusAsync(Guid recordId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigning/status/{recordId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var cosigning = await response.Content.ReadFromJsonAsync<Cosigning>();
                    _logger.LogInformation("Retrieved cosigning status for record {RecordId}", recordId);
                    return cosigning;
                }
                else
                {
                    _logger.LogWarning("Failed to get cosigning status for record {RecordId}. Status: {StatusCode}", recordId, response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cosigning status for record {RecordId}", recordId);
                throw;
            }
        }

        public async Task<CosigningRequest> GetCosigningRequestStatusAsync(Guid recordId, Guid organizationId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_encounterNotesURL}/api/cosigning/request-status/{recordId}/{organizationId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var request = await response.Content.ReadFromJsonAsync<CosigningRequest>();
                    _logger.LogInformation("Retrieved cosigning request status for record {RecordId}", recordId);
                    return request;
                }
                else
                {
                    _logger.LogWarning("Failed to get cosigning request status for record {RecordId}. Status: {StatusCode}", recordId, response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cosigning request status for record {RecordId}", recordId);
                throw;
            }
        }
    }
}
