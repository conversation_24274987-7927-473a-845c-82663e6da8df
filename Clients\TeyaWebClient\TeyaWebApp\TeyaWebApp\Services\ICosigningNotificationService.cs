using System;
using System.Threading.Tasks;

namespace TeyaWebApp.Services
{
    /// <summary>
    /// Interface for cosigning notification service
    /// </summary>
    public interface ICosigningNotificationService
    {
        /// <summary>
        /// Start the SignalR connection
        /// </summary>
        Task StartAsync();

        /// <summary>
        /// Stop the SignalR connection
        /// </summary>
        Task StopAsync();

        /// <summary>
        /// Check if the connection is active
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// Event fired when a new cosigning request notification is received
        /// </summary>
        event Func<CosigningNotificationData, Task> OnNewCosigningRequest;

        /// <summary>
        /// Event fired when a request approval notification is received
        /// </summary>
        event Func<CosigningNotificationData, Task> OnRequestApproved;

        /// <summary>
        /// Event fired when a request comment notification is received
        /// </summary>
        event Func<CosigningNotificationData, Task> OnRequestCommented;

        /// <summary>
        /// Event fired when a comment resolution notification is received
        /// </summary>
        event Func<CosigningNotificationData, Task> OnCommentResolved;
    }

    /// <summary>
    /// Data structure for cosigning notifications
    /// </summary>
    public class CosigningNotificationData
    {
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string DetailedMessage { get; set; } = string.Empty;
        public string RequesterName { get; set; } = string.Empty;
        public string ReviewerName { get; set; } = string.Empty;
        public string ResolverName { get; set; } = string.Empty;
        public string PatientName { get; set; } = string.Empty;
        public Guid RequestId { get; set; }
        public int CommentCount { get; set; }
        public string Priority { get; set; } = string.Empty;
        public bool ActionRequired { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
