@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@using Syncfusion.Blazor.RichTextEditor

<style>
    .review-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
    }

    .review-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .review-header {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;
        padding: 24px;
        border-bottom: 1px solid #e0e0e0;
    }

    .notes-display {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        max-height: 400px;
        overflow-y: auto;
    }

    .comment-section {
        border-top: 1px solid #e0e0e0;
        padding: 20px;
        background: #fafafa;
    }

    .comment-item {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        position: relative;
    }

    .comment-resolved {
        background: #f1f8e9;
        border-color: #4caf50;
    }

    .comment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .comment-author {
        font-weight: 600;
        color: #1976d2;
    }

    .comment-date {
        font-size: 0.875rem;
        color: #666;
    }

    .comment-content {
        margin: 8px 0;
        line-height: 1.5;
    }

    .comment-actions {
        display: flex;
        gap: 8px;
        margin-top: 12px;
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        padding: 20px;
        background: white;
        border-top: 1px solid #e0e0e0;
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .status-pending {
        background: #fff3e0;
        color: #f57c00;
    }

    .status-approved {
        background: #e8f5e8;
        color: #2e7d32;
    }

    .status-changes-requested {
        background: #ffebee;
        color: #c62828;
    }

    .rte-container {
        border: 1px solid #ddd;
        border-radius: 8px;
        margin: 16px 0;
    }

    .patient-info {
        background: #e3f2fd;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .section-divider {
        height: 1px;
        background: linear-gradient(90deg, transparent, #ddd, transparent);
        margin: 24px 0;
    }
</style>

<div class="review-container">
    <MudContainer MaxWidth="MaxWidth.Large">
        <MudPaper Class="review-card" Elevation="8">
            <!-- Header -->
            <div class="review-header">
                <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                        <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Large" />
                        <div>
                            <MudText Typo="Typo.h5" Style="font-weight: 600; margin-bottom: 4px;">
                                @Localizer["CosigningReview"]
                            </MudText>
                            <MudText Typo="Typo.body2" Style="opacity: 0.9;">
                                @Localizer["ReviewAndApproveDocumentation"]
                            </MudText>
                        </div>
                    </MudStack>
                    @if (CurrentRequest != null)
                    {
                        <div class="status-badge @GetStatusClass(CurrentRequest.Status)">
                            @GetStatusText(CurrentRequest.Status)
                        </div>
                    }
                </MudStack>
            </div>

            <!-- Patient Information -->
            @if (CurrentRequest != null)
            {
                <MudCardContent>
                    <div class="patient-info">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="4">
                            <MudIcon Icon="@Icons.Material.Filled.Person" Color="Color.Primary" />
                            <div>
                                <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                    @CurrentRequest.PatientName
                                </MudText>
                                <MudText Typo="Typo.body2" Style="color: #666;">
                                    @CurrentRequest.PatientAge • @CurrentRequest.PatientGender
                                </MudText>
                            </div>
                            <MudSpacer />
                            <MudStack AlignItems="AlignItems.End">
                                <MudText Typo="Typo.body2" Style="color: #666;">
                                    @Localizer["RequestedBy"]: @CurrentRequest.RequesterName
                                </MudText>
                                <MudText Typo="Typo.caption" Style="color: #999;">
                                    @CurrentRequest.RequestDate.ToString("MMM dd, yyyy 'at' HH:mm")
                                </MudText>
                            </MudStack>
                        </MudStack>
                    </div>

                    <!-- Notes Display Section -->
                    <MudText Typo="Typo.h6" Style="font-weight: 600; margin-bottom: 16px;">
                        @Localizer["MedicalDocumentation"]
                    </MudText>

                    @if (IsLoadingNotes)
                    {
                        <MudProgressLinear Indeterminate="true" Color="Color.Primary" />
                    }
                    else
                    {
                        <TeyaWebApp.Components.Shared.UnifiedNotesDisplay NoteSections="@NotesContent"
                                             IsReadOnly="@(!IsCommentMode)"
                                             IsCommentMode="@IsCommentMode"
                                             OnContentChanged="@HandleNoteContentChange"
                                             OnTextSelected="@HandleTextSelection"
                                             InitiallyExpanded="@(new List<string> { "ChiefComplaint", "HPI" })" />
                    }

                    <div class="section-divider"></div>

                    <!-- Comments Section -->
                    <MudText Typo="Typo.h6" Style="font-weight: 600; margin-bottom: 16px;">
                        @Localizer["ReviewComments"]
                    </MudText>

                    <div class="comment-section">
                        @if (Comments.Any())
                        {
                            @foreach (var comment in Comments.OrderBy(c => c.CommentDate))
                            {
                                <div class="comment-item @(comment.IsResolved ? "comment-resolved" : "")">
                                    <div class="comment-header">
                                        <span class="comment-author">@comment.CommenterName</span>
                                        <span class="comment-date">@comment.CommentDate.ToString("MMM dd, yyyy 'at' HH:mm")</span>
                                    </div>
                                    <div class="comment-content">
                                        @((MarkupString)comment.Comment)
                                    </div>
                                    @if (!string.IsNullOrEmpty(comment.SelectedText))
                                    {
                                        <MudAlert Severity="Severity.Info" Dense="true" Class="mt-2">
                                            <strong>@Localizer["ReferenceText"]:</strong> "@comment.SelectedText"
                                        </MudAlert>
                                    }
                                    @if (comment.IsResolved)
                                    {
                                        <MudChip T="string" Size="Size.Small" Color="Color.Success" Icon="@Icons.Material.Filled.CheckCircle">
                                            @Localizer["ResolvedBy"] @comment.ResolvedByName
                                        </MudChip>
                                    }
                                    else if (CanResolveComment(comment))
                                    {
                                        <div class="comment-actions">
                                            <MudButton Size="Size.Small" 
                                                       Color="Color.Success" 
                                                       Variant="Variant.Outlined"
                                                       StartIcon="@Icons.Material.Filled.CheckCircle"
                                                       OnClick="@(() => ResolveComment(comment.Id))">
                                                @Localizer["Resolve"]
                                            </MudButton>
                                        </div>
                                    }
                                </div>
                            }
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Info">
                                @Localizer["NoCommentsYet"]
                            </MudAlert>
                        }

                        <!-- Add Comment Section -->
                        @if (CurrentRequest?.Status == CosigningRequestStatus.Pending)
                        {
                            <MudExpansionPanels Class="mt-4">
                                <MudExpansionPanel Text="@Localizer["AddComment"]" Icon="@Icons.Material.Filled.Comment">
                                    <div class="rte-container">
                                        <SfRichTextEditor @ref="commentEditor"
                                                          Value="@NewCommentContent"
                                                          ValueChanged="@((string newValue) => NewCommentContent = newValue)"
                                                          Placeholder="@Localizer["EnterYourComment"]">
                                            <RichTextEditorToolbarSettings Items="@GetCommentToolbarItems()" />
                                        </SfRichTextEditor>
                                    </div>
                                    <MudStack Row Justify="Justify.FlexEnd" Class="mt-3" Spacing="2">
                                        <MudButton Color="Color.Primary"
                                                   Variant="Variant.Filled"
                                                   StartIcon="@Icons.Material.Filled.Comment"
                                                   OnClick="AddComment"
                                                   Disabled="@(string.IsNullOrWhiteSpace(NewCommentContent) || IsProcessing)">
                                            @Localizer["AddComment"]
                                        </MudButton>
                                    </MudStack>
                                </MudExpansionPanel>
                            </MudExpansionPanels>
                        }
                    </div>
                </MudCardContent>
            }

            <!-- Action Buttons -->
            @if (CurrentRequest?.Status == CosigningRequestStatus.Pending)
            {
                <div class="action-buttons">
                    <MudButton Color="Color.Success"
                               Variant="Variant.Filled"
                               Size="Size.Large"
                               StartIcon="@Icons.Material.Filled.CheckCircle"
                               OnClick="ApproveRequest"
                               Disabled="@IsProcessing">
                        @Localizer["ApproveAndCosign"]
                    </MudButton>

                    <MudButton Color="Color.Warning"
                               Variant="Variant.Outlined"
                               Size="Size.Large"
                               StartIcon="@Icons.Material.Filled.Comment"
                               OnClick="ToggleCommentMode"
                               Disabled="@IsProcessing">
                        @(IsCommentMode ? Localizer["ExitCommentMode"] : Localizer["RequestChanges"])
                    </MudButton>

                    <MudSpacer />

                    <MudButton Color="Color.Default"
                               Variant="Variant.Text"
                               Size="Size.Large"
                               StartIcon="@Icons.Material.Filled.Close"
                               OnClick="CloseReview">
                        @Localizer["Close"]
                    </MudButton>
                </div>
            }
        </MudPaper>
    </MudContainer>
</div>

<!-- Loading Overlay -->
@if (IsProcessing)
{
    <MudOverlay Visible="true" DarkBackground="true" Absolute="false">
        <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
    </MudOverlay>
}
