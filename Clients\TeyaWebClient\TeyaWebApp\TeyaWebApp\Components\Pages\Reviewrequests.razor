﻿@page "/reviewrequests"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaWebApp.TeyaAIScribeResource.TeyaAIScribeResource> Localizer
@using TeyaWebApp.TeyaAIScribeResource
@using TeyaWebApp.Components.Layout
@using Syncfusion.Blazor.RichTextEditor
@layout Admin



<PageTitle>@Localizer["ReviewRequests"]</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="pa-6">
    <MudPaper Class="pa-6" Elevation="2" Style="border-radius: 8px;">
        <!-- Header Section -->
        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="mb-4">
            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                <MudIcon Icon="@Icons.Material.Filled.RateReview" Color="Color.Primary" Size="Size.Large" />
                <MudText Typo="Typo.h4" Style="font-weight: 600; color: #1976d2;">
                    @Localizer["ReviewRequests"]
                </MudText>
            </MudStack>
            <MudStack Row Spacing="2">
                <MudChip T="string" Color="Color.Warning" Size="Size.Medium" Icon="@Icons.Material.Filled.Schedule">
                    @Localizer["Pending"]: @_reviewRequests.Count(r => r.Status == CosigningRequestStatus.Pending)
                </MudChip>
                <MudChip T="string" Color="Color.Info" Size="Size.Medium" Icon="@Icons.Material.Filled.Info">
                    @Localizer["Total"]: @_reviewRequests.Count
                </MudChip>
            </MudStack>
        </MudStack>

        <MudDivider Class="mb-4" />

        <!-- Show table only when review form is not open -->
        @if (!_showReviewForm)
        {
            <!-- Loading State -->
            @if (_isLoading)
            {
                <MudStack AlignItems="AlignItems.Center" Spacing="3" Class="pa-8">
                    <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                    <MudText Typo="Typo.body1">@Localizer["LoadingRequests"]</MudText>
                </MudStack>
            }
            else if (!_reviewRequests.Any())
            {
                <!-- Empty State -->
                <MudStack AlignItems="AlignItems.Center" Spacing="3" Class="pa-8">
                    <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Large" Style="color: #9e9e9e;" />
                    <MudText Typo="Typo.h6" Style="color: #9e9e9e;">@Localizer["NoReviewRequestsFound"]</MudText>
                    <MudText Typo="Typo.body2" Style="color: #9e9e9e;">@Localizer["NoIncomingRequestsMessage"]</MudText>
                </MudStack>
            }
            else
            {
                <!-- Requests Table -->
                <MudTable Items="@_reviewRequests"
                          Hover="true"
                          Striped="true"
                          Dense="true"
                          FixedHeader="true"
                          Height="600px"
                          Class="requests-table">
                <HeaderContent>
                    <MudTh Style="font-weight: 600;">@Localizer["Patient"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Age"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Gender"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Requester"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Status"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["RequestDate"]</MudTh>
                    <MudTh Style="font-weight: 600;">@Localizer["Actions"]</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="Patient">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Color="Color.Primary" />
                            <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.PatientName</MudText>
                        </MudStack>
                    </MudTd>
                    <MudTd DataLabel="Age">@context.PatientAge</MudTd>
                    <MudTd DataLabel="Gender">@context.PatientGender</MudTd>
                    <MudTd DataLabel="Requester">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Size="Size.Small" Color="Color.Secondary" />
                            <MudText Typo="Typo.body2">@context.RequesterName</MudText>
                        </MudStack>
                    </MudTd>
                    <MudTd DataLabel="Status">
                        <MudChip T="string"
                                 Color="@GetStatusColor(context.Status)"
                                 Size="Size.Small"
                                 Icon="@GetStatusIcon(context.Status)"
                                 Variant="Variant.Filled">
                            @GetStatusText(context.Status)
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="RequestDate">
                        <MudText Typo="Typo.body2">@context.RequestDate.ToString("MM/dd/yyyy")</MudText>
                        <MudText Typo="Typo.caption" Class="text-muted">@context.RequestDate.ToString("HH:mm")</MudText>
                    </MudTd>
                    <MudTd DataLabel="Actions">
                        <MudStack Row Spacing="1">
                            @if (context.Status == CosigningRequestStatus.Pending)
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.RateReview"
                                               Color="Color.Success"
                                               Size="Size.Small"
                                               OnClick="@(async () => await OpenReviewForm(context))"
                                               Title="@Localizer["Review"]" />
                            }
                            else if (context.Status == CosigningRequestStatus.ChangesRequested)
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Comment"
                                               Color="Color.Warning"
                                               Size="Size.Small"
                                               OnClick="@(() => ViewComments(context))"
                                               Title="@Localizer["ViewComments"]" />
                            }
                        </MudStack>
                    </MudTd>
                </RowTemplate>
            </MudTable>
            }
        }
    </MudPaper>

    <!-- GitHub-style Review Form -->
    @if (_showReviewForm )
    {
        <MudPaper Class="pa-6 mt-4" Elevation="3" Style="border-radius: 8px; border-left: 4px solid #1976d2;">
            <!-- Review Header -->
            <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="mb-4">
                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                    <MudIcon Icon="@Icons.Material.Filled.RateReview" Color="Color.Primary" Size="Size.Large" />
                    <MudText Typo="Typo.h5" Style="font-weight: 600; color: #1976d2;">
                        @Localizer["ReviewingNotes"] - @_selectedRequest.PatientName
                    </MudText>
                </MudStack>
                <MudStack Row Spacing="2">
                    <MudButton Variant="Variant.Text"
                               Color="Color.Default"
                               OnClick="CloseReviewForm"
                               StartIcon="@Icons.Material.Filled.Cancel">
                        @Localizer["Cancel"]
                    </MudButton>
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Success"
                               OnClick="ApproveRequest"
                               StartIcon="@Icons.Material.Filled.CheckCircle"
                               Disabled="@_isProcessing">
                        @Localizer["Approve"]
                    </MudButton>
                </MudStack>
            </MudStack>

            <MudDivider Class="mb-4" />

            

            <!-- Notes Content (EXACT COPY from Notes page) -->
            @if (_isLoadingRecord)
            {
                <MudStack AlignItems="AlignItems.Center" Spacing="3" Class="pa-8">
                    <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
                    <MudText Typo="Typo.body1">@Localizer["LoadingNotes"]</MudText>
                </MudStack>
            }
            else
            {
                <MudPaper Class="pa-4" Elevation="1" Style="background-color: #ffffff; border: 1px solid #e0e0e0;">
                    @{
                        var NotesData = ExtractNotesData(_selectedRecord.Notes);
                    }

                    <div class="notes-layout-container no-transcription">
                        <!-- First Column: Notes (EXACT COPY from Notes page) -->
                        <div class="notes-column">
                            @foreach (var section in NotesData)
                            {
                                @foreach (var kvp in section)
                                {
                                    <MudCard Class="mt-3 pa-4">
                                        <p class="section-heading">
                                            @kvp.Key
                                        </p>
                                        @foreach (var data in kvp.Value.Where(d => d.Key != "Transcription"))
                                        {
                                            <p class="subsection-heading" style="font-size: 1rem; color: #333; font-weight: 500; margin-top: 12px;">
                                                @data.Key
                                            </p>

                                            <div class="description-container">
                                                <div class="editor-container">
                                                    @{
                                                        var editorKey = $"{_selectedRecord.Id}{kvp.Key}{data.Key}";
                                                        var content = GetEditorContent(_selectedRecord, kvp.Key, data.Key);
                                                        var editorContent = string.IsNullOrEmpty(content) ? "<div>No content...</div>" : content;
                                                    }
                                                    <SfRichTextEditor Value="@editorContent"
                                                                      ReadOnly="true"
                                                                      SaveInterval="1000">
                                                        <RichTextEditorToolbarSettings Items="@GetReviewToolbarItems()">
                                                            <RichTextEditorCustomToolbarItems>
                                                                <RichTextEditorCustomToolbarItem Name="addComment">
                                                                    <Template>
                                                                        <MudIconButton Icon="@Icons.Material.Filled.Comment"
                                                                                       Color="Color.Primary"
                                                                                       Size="Size.Small"
                                                                                       OnClick="@(() => AddSubsectionComment(kvp.Key, data.Key))"
                                                                                       Title="@Localizer["AddComment"]" />
                                                                    </Template>
                                                                </RichTextEditorCustomToolbarItem>
                                                            </RichTextEditorCustomToolbarItems>
                                                        </RichTextEditorToolbarSettings>
                                                    </SfRichTextEditor>
                                                </div>
                                            </div>

                                            <!-- Show existing comments for this subsection -->
                                            @if (_sectionComments.ContainsKey($"{kvp.Key}|{data.Key}"))
                                            {
                                                <div class="comments-section mt-2">
                                                    @foreach (var comment in _sectionComments[$"{kvp.Key}|{data.Key}"])
                                                    {
                                                        <MudPaper Class="pa-3 mt-2" Elevation="1" Style="background-color: #f0f7ff; border-left: 3px solid #1976d2;">
                                                            <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                                                                <MudText Typo="Typo.caption" Style="font-weight: 600; color: #1976d2;">
                                                                    @comment.CommenterName
                                                                </MudText>
                                                                <MudText Typo="Typo.caption" Style="color: #666;">
                                                                    @comment.CommentDate.ToString("MM/dd/yyyy HH:mm")
                                                                </MudText>
                                                            </MudStack>
                                                            <MudText Typo="Typo.body2" Class="mt-1">
                                                                @comment.Comment
                                                            </MudText>
                                                        </MudPaper>
                                                    }
                                                </div>
                                            }
                                        }
                                    </MudCard>
                                }
                            }
                        </div>
                    </div>
                </MudPaper>
            }
        </MudPaper>
    }
</MudContainer>

<!-- Comment Dialog -->
<MudDialog @bind-IsVisible="_showCommentDialog" Options="_commentDialogOptions">
    <TitleContent>
        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
            <MudIcon Icon="@Icons.Material.Filled.Comment" Color="Color.Primary" />
            <MudText Typo="Typo.h6">@Localizer["AddComment"]</MudText>
        </MudStack>
    </TitleContent>
    <DialogContent>
        <MudStack Spacing="3">
            <MudText Typo="Typo.body2" Style="color: #666;">
                @Localizer["CommentingOn"]: <strong>@_commentContext</strong>
            </MudText>
            <MudTextField @bind-Value="_newComment"
                          Label="@Localizer["Comment"]"
                          Placeholder="@Localizer["EnterYourComment"]"
                          Lines="4"
                          Variant="Variant.Outlined"
                          HelperText="@Localizer["CommentHelperText"]" />
        </MudStack>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseCommentDialog">@Localizer["Cancel"]</MudButton>
        <MudButton Color="Color.Primary"
                   Variant="Variant.Filled"
                   OnClick="SubmitComment"
                   Disabled="@(string.IsNullOrWhiteSpace(_newComment) || _isProcessing)">
            @Localizer["AddComment"]
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .requests-table {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .text-muted {
        color: #6c757d;
    }

    .mud-table-row:hover {
        background-color: rgba(25, 118, 210, 0.04) !important;
        cursor: pointer;
    }

    /* GitHub-style review form styles */
    .review-section {
        margin-bottom: 1rem;
    }

    .review-card {
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        transition: box-shadow 0.2s ease;
    }

    .review-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .review-subsection {
        border-left: 3px solid #f6f8fa;
        padding-left: 1rem;
        margin: 0.5rem 0;
    }

    .review-content {
        background-color: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 1rem;
        margin: 0.5rem 0;
        position: relative;
    }

    .content-display {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #24292e;
    }

    .comments-section {
        border-top: 1px solid #e1e4e8;
        padding-top: 0.5rem;
    }

    .section-heading {
        color: #1976d2 !important;
        font-weight: 600 !important;
        border-bottom: 2px solid #e1e4e8;
        padding-bottom: 0.5rem;
    }

    /* Comment dialog styles */
    .mud-dialog {
        max-width: 600px;
    }

    /* EXACT COPY from Notes page CSS */
    .description-container {
        margin-bottom: 16px;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        overflow: hidden;
    }

    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 12px;
        background-color: #f9f9f9;
        cursor: pointer;
        transition: all 0.2s ease;
        min-height: 40px;
        display: flex;
        align-items: center;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        overflow: hidden;
    }

        .description-box:hover {
            background-color: #f0f0f0;
            border-color: #bbb;
        }

        .description-box.empty {
            color: #999;
            font-style: italic;
        }

    .description-content {
        min-height: 24px;
    }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        transition: all 0.2s ease;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        min-width: 400px;
    }

        .editor-container:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

    /* Layout using CSS Grid */
    .notes-layout-container {
        display: grid;
        gap: 1rem;
        width: 100%;
        max-width: 100%;
        overflow: hidden;
        align-items: start;
        box-sizing: border-box;
    }

        .notes-layout-container.no-transcription {
            grid-template-columns: 1fr;
        }

        .notes-layout-container.has-transcription {
            grid-template-columns: minmax(0, 70%) minmax(0, 30%);
        }

    .notes-column {
        min-width: 0;
        overflow: hidden;
        width: 100%;
        box-sizing: border-box;
    }

    .notes-layout-container > * {
        min-width: 0;
    }

    /* Syncfusion RTE specific overrides (EXACT COPY from Notes page) */
    .e-richtexteditor {
        border: none !important;
        border-radius: 0 0 4px 4px !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }

    .e-rte-toolbar {
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #ddd !important;
        padding: 4px 8px !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        min-height: 40px !important;
        max-height: 40px !important;
        overflow: visible !important;
    }

    .e-toolbar-items {
        background: transparent !important;
        flex: 1 !important;
        max-width: calc(100% - 45px) !important;
        overflow: visible !important;
        display: flex !important;
        justify-content: flex-start !important;
        flex-wrap: nowrap !important;
        gap: 4px !important;
        align-items: center !important;
    }

    .e-toolbar-item {
        margin: 0 2px !important;
        flex-shrink: 1 !important;
        min-width: 28px !important;
    }

    .e-toolbar-item .e-tbar-btn {
        padding: 4px 6px !important;
        min-width: 28px !important;
        height: 32px !important;
        font-size: 14px !important;
    }

    .e-rte-content {
        min-height: 150px !important;
        max-height: 400px !important;
        overflow-y: auto !important;
        padding: 12px !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .review-content {
            padding: 0.5rem;
        }

        .review-subsection {
            padding-left: 0.5rem;
        }
    }
</style>