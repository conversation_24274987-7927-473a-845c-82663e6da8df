using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using MudBlazor;
using System;
using System.Threading.Tasks;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Layout
{
    /// <summary>
    /// Code-behind for MainLayout component
    /// Handles SignalR notifications for cosigning
    /// </summary>
    public partial class MainLayout : IAsyncDisposable
    {
        [Inject] private ICosigningNotificationService NotificationService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ILogger<MainLayout> Logger { get; set; }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                if (NotificationService != null)
                {
                    // Subscribe to notification events
                    NotificationService.OnNewCosigningRequest += HandleNewCosigningRequest;
                    NotificationService.OnRequestApproved += HandleRequestApproved;
                    NotificationService.OnRequestCommented += HandleRequestCommented;
                    NotificationService.OnCommentResolved += HandleCommentResolved;

                    // Start SignalR connection
                    await NotificationService.StartAsync();
                    Logger.LogInformation("SignalR notification service started");
                }
                else
                {
                    Logger.LogWarning("NotificationService is null, skipping SignalR initialization");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing SignalR notification service");
            }
        }

        private async Task HandleNewCosigningRequest(CosigningNotificationData notification)
        {
            try
            {
                await InvokeAsync(() =>
                {
                    Snackbar.Add(notification.Message, Severity.Info, config =>
                    {
                        config.Icon = Icons.Material.Filled.RateReview;
                        config.VisibleStateDuration = 5000;
                        config.ShowTransitionDuration = 300;
                        config.HideTransitionDuration = 300;
                        config.Action = "View";
                        config.ActionColor = Color.Primary;
                        config.Onclick = snackbar =>
                        {
                            // Navigate to review requests page
                            return Task.CompletedTask;
                        };
                    });
                    StateHasChanged();
                });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling new cosigning request notification");
            }
        }

        private async Task HandleRequestApproved(CosigningNotificationData notification)
        {
            try
            {
                await InvokeAsync(() =>
                {
                    Snackbar.Add(notification.Message, Severity.Success, config =>
                    {
                        config.Icon = Icons.Material.Filled.CheckCircle;
                        config.VisibleStateDuration = 5000;
                        config.ShowTransitionDuration = 300;
                        config.HideTransitionDuration = 300;
                    });
                    StateHasChanged();
                });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling request approved notification");
            }
        }

        private async Task HandleRequestCommented(CosigningNotificationData notification)
        {
            try
            {
                await InvokeAsync(() =>
                {
                    Snackbar.Add(notification.Message, Severity.Warning, config =>
                    {
                        config.Icon = Icons.Material.Filled.Comment;
                        config.VisibleStateDuration = 7000;
                        config.ShowTransitionDuration = 300;
                        config.HideTransitionDuration = 300;
                        config.Action = "View";
                        config.ActionColor = Color.Primary;
                        config.Onclick = snackbar =>
                        {
                            // Navigate to my requests page
                            return Task.CompletedTask;
                        };
                    });
                    StateHasChanged();
                });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling request commented notification");
            }
        }

        private async Task HandleCommentResolved(CosigningNotificationData notification)
        {
            try
            {
                await InvokeAsync(() =>
                {
                    Snackbar.Add(notification.Message, Severity.Info, config =>
                    {
                        config.Icon = Icons.Material.Filled.TaskAlt;
                        config.VisibleStateDuration = 5000;
                        config.ShowTransitionDuration = 300;
                        config.HideTransitionDuration = 300;
                        config.Action = "Review";
                        config.ActionColor = Color.Primary;
                        config.Onclick = snackbar =>
                        {
                            // Navigate to review requests page
                            return Task.CompletedTask;
                        };
                    });
                    StateHasChanged();
                });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling comment resolved notification");
            }
        }

        public async ValueTask DisposeAsync()
        {
            try
            {
                // Unsubscribe from events
                if (NotificationService != null)
                {
                    NotificationService.OnNewCosigningRequest -= HandleNewCosigningRequest;
                    NotificationService.OnRequestApproved -= HandleRequestApproved;
                    NotificationService.OnRequestCommented -= HandleRequestCommented;
                    NotificationService.OnCommentResolved -= HandleCommentResolved;

                    await NotificationService.StopAsync();
                }
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error disposing MainLayout");
            }
        }
    }
}
