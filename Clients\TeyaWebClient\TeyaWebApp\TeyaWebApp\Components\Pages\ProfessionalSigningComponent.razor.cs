using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Professional signing component with streamlined workflow
    /// </summary>
    public partial class ProfessionalSigningComponent : ComponentBase
    {
        #region Parameters
        [Parameter] public Guid RecordId { get; set; }
        [Parameter] public Guid PatientId { get; set; }
        [Parameter] public string PatientName { get; set; } = string.Empty;
        [Parameter] public Guid OrganizationId { get; set; }
        [Parameter] public bool Subscription { get; set; }
        [Parameter] public EventCallback<Cosigning> OnSignatureUpdated { get; set; }
        #endregion

        #region Injected Services
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        [Inject] private ICosigningService CosigningService { get; set; }
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }
        [Inject] private IMemberService MemberService { get; set; }
        [Inject] private ILogger<ProfessionalSigningComponent> Logger { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IStringLocalizer<ProfessionalSigningComponent> Localizer { get; set; }
        #endregion

        #region Private Fields
        private Cosigning CurrentCosigning = new();
        private CosigningRequest? ActiveRequest = null;
        private List<Member> AvailableProviders = new();
        
        private bool IsProcessing = false;
        private bool IsNewRecord = true;
        private bool HasActiveRequest = false;
        private bool ShowSuccessDialog = false;
        private string SuccessMessage = string.Empty;
        
        private SigningAction SelectedAction = SigningAction.Lock;
        private Member? SelectedProvider = null;
        #endregion

        #region Lifecycle Methods
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadCosigningStatus();
                await LoadActiveRequest();
                await LoadProviders();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing professional signing component for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorInitializingComponent"], Severity.Error);
            }
        }

        protected override async Task OnParametersSetAsync()
        {
            if (RecordId != Guid.Empty)
            {
                await LoadCosigningStatus();
                await LoadActiveRequest();
            }
        }
        #endregion

        #region Data Loading Methods
        private async Task LoadCosigningStatus()
        {
            try
            {
                var existingCosigning = await CosigningService.GetCosigningStatusAsync(RecordId, OrganizationId, Subscription);

                if (existingCosigning != null && existingCosigning.Id != Guid.Empty)
                {
                    CurrentCosigning = existingCosigning;
                    IsNewRecord = false;
                }
                else
                {
                    CurrentCosigning = new Cosigning
                    {
                        RecordId = RecordId,
                        OrganizationId = OrganizationId,
                        IsSigned = false,
                        IsCosigned = false,
                        IsLocked = false
                    };
                    IsNewRecord = true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading cosigning status for record {RecordId}", RecordId);
                throw;
            }
        }

        private async Task LoadActiveRequest()
        {
            try
            {
                ActiveRequest = await CosigningRequestService.GetActiveRequestAsync(RecordId, OrganizationId, Subscription);
                HasActiveRequest = ActiveRequest != null;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading active request for record {RecordId}", RecordId);
                HasActiveRequest = false;
                ActiveRequest = null;
            }
        }

        private async Task LoadProviders()
        {
            try
            {
                var providers = await MemberService.GetAllMembersAsync(OrganizationId, UserContext.ActiveUserSubscription);
                AvailableProviders = providers?.Where(p => p.Id != Guid.Parse(CurrentUser.id)).ToList() ?? new List<Member>();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading providers for organization {OrganizationId}", OrganizationId);
                AvailableProviders = new List<Member>();
            }
        }
        #endregion

        #region Action Methods
        private async Task SignDocument()
        {
            try
            {
                IsProcessing = true;
                StateHasChanged();

                var userId = Guid.Parse(CurrentUser.id);
                var userName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";

                await CosigningService.SignNoteAsync(RecordId, userId, userName, OrganizationId, Subscription);

                // Reload status
                await LoadCosigningStatus();
                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);

                SuccessMessage = Localizer["DocumentSignedSuccessfully"];
                ShowSuccessDialog = true;
                
                Logger.LogInformation("Document signed successfully for record {RecordId}", RecordId);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error signing document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorSigningDocument"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }

        private async Task LockDocument()
        {
            try
            {
                IsProcessing = true;
                StateHasChanged();

                var userId = Guid.Parse(CurrentUser.id);
                var userName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";

                await CosigningService.LockNoteAsync(RecordId, userId, userName, OrganizationId, Subscription);

                // Reload status
                await LoadCosigningStatus();
                await OnSignatureUpdated.InvokeAsync(CurrentCosigning);

                SuccessMessage = Localizer["DocumentLockedSuccessfully"];
                ShowSuccessDialog = true;
                
                Logger.LogInformation("Document locked successfully for record {RecordId}", RecordId);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error locking document for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorLockingDocument"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }

        private async Task RequestCosigning()
        {
            if (SelectedProvider == null) return;

            try
            {
                IsProcessing = true;
                StateHasChanged();

                var userId = Guid.Parse(CurrentUser.id);
                var userName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";

                await CosigningService.RequestCosigningAsync(
                    RecordId,
                    userId,
                    userName,
                    SelectedProvider.Id,
                    SelectedProvider.UserName,
                    OrganizationId,
                    Subscription);

                // Reload status
                await LoadActiveRequest();

                SuccessMessage = Localizer["CosigningRequestSentSuccessfully"];
                ShowSuccessDialog = true;
                
                Logger.LogInformation("Cosigning request sent for record {RecordId} to provider {ProviderId}", RecordId, SelectedProvider.Id);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error requesting cosigning for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorRequestingCosigning"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }

        private async Task CancelRequest()
        {
            if (ActiveRequest == null) return;

            try
            {
                IsProcessing = true;
                StateHasChanged();

                await CosigningRequestService.CancelRequestAsync(
                    ActiveRequest.Id,
                    OrganizationId,
                    Subscription);

                // Reload status
                await LoadActiveRequest();

                Snackbar.Add(Localizer["RequestCancelledSuccessfully"], Severity.Success);
                
                Logger.LogInformation("Cosigning request cancelled for record {RecordId}", RecordId);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error cancelling request for record {RecordId}", RecordId);
                Snackbar.Add(Localizer["ErrorCancellingRequest"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }
        #endregion

        #region Helper Methods
        private async Task<IEnumerable<Member>> SearchProviders(string searchText, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return AvailableProviders.Take(10);

            return AvailableProviders
                .Where(p => p.UserName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                           p.Email.Contains(searchText, StringComparison.OrdinalIgnoreCase))
                .Take(10);
        }

        private int GetCurrentStep()
        {
            if (CurrentCosigning.IsLocked) return 4;
            if (HasActiveRequest) return 3;
            if (CurrentCosigning.IsSigned) return 2;
            return 1;
        }

        private string GetStepClass(int stepNumber)
        {
            var currentStep = GetCurrentStep();
            if (stepNumber < currentStep) return "step-completed";
            if (stepNumber == currentStep) return "step-active";
            return "step-pending";
        }

        private string GetSignatureDisplayClass()
        {
            if (CurrentCosigning.IsLocked) return "locked";
            if (CurrentCosigning.IsSigned) return "signed";
            return "";
        }

        private string GetStatusIndicatorClass()
        {
            if (CurrentCosigning.IsLocked) return "status-locked";
            if (CurrentCosigning.IsSigned) return "status-signed";
            return "status-pending";
        }

        private string GetStatusText()
        {
            if (CurrentCosigning.IsLocked) return Localizer["Locked"];
            if (CurrentCosigning.IsSigned) return Localizer["Signed"];
            return Localizer["Pending"];
        }

        private string GetSignatureText()
        {
            var lines = new List<string>();

            if (CurrentCosigning.IsSigned)
            {
                lines.Add($"{Localizer["ElectronicallySignedBy"]} {CurrentCosigning.SignerName}");
                lines.Add($"{Localizer["SignedOn"]} {CurrentCosigning.Date:MMM dd, yyyy 'at' HH:mm}");
            }

            if (CurrentCosigning.IsCosigned && CurrentCosigning.CosignerId.HasValue)
            {
                lines.Add($"{Localizer["CosignedBy"]} {CurrentCosigning.CosignerName}");
            }

            if (CurrentCosigning.IsLocked)
            {
                lines.Add($"{Localizer["LockedOn"]} {CurrentCosigning.LastUpdated:MMM dd, yyyy 'at' HH:mm}");
            }

            return string.Join("\n", lines);
        }
        #endregion
    }
}
