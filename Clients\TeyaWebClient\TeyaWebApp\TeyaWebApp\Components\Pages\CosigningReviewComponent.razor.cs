using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using Syncfusion.Blazor.RichTextEditor;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    /// <summary>
    /// Professional cosigning review component with GitHub-style interface
    /// </summary>
    public partial class CosigningReviewComponent : ComponentBase
    {
        #region Parameters
        [Parameter] public Guid RequestId { get; set; }
        [Parameter] public EventCallback OnReviewCompleted { get; set; }
        [Parameter] public EventCallback OnReviewClosed { get; set; }
        #endregion

        #region Injected Services
        [Inject] private ActiveUser CurrentUser { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        [Inject] private ICosigningRequestService CosigningRequestService { get; set; }
        [Inject] private IRecordService RecordService { get; set; }
        [Inject] private ILogger<CosigningReviewComponent> Logger { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IStringLocalizer<CosigningReviewComponent> Localizer { get; set; }
        [Inject] private IMemberService MemberService { get; set; }
        [Inject] private IProgressNotesService ProgressNotesService { get; set; }
        #endregion

        #region Private Fields
        private CosigningRequest? CurrentRequest = null;
        private List<CosigningComment> Comments = new();
        private List<TeyaWebApp.Components.Shared.UnifiedNotesDisplay.NoteSection> NotesContent = new();
        private Dictionary<string, SfRichTextEditor> RteReferences = new();
        private SfRichTextEditor? commentEditor;
        
        private bool IsProcessing = false;
        private bool IsLoadingNotes = false;
        private bool IsCommentMode = false;
        private string NewCommentContent = string.Empty;
        private string SelectedText = string.Empty;
        #endregion

        #region Lifecycle Methods
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadReviewRequest();
                await LoadNotesContent();
                await LoadComments();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing cosigning review component for request {RequestId}", RequestId);
                Snackbar.Add(Localizer["ErrorLoadingReview"], Severity.Error);
            }
        }

        protected override async Task OnParametersSetAsync()
        {
            if (RequestId != Guid.Empty && (CurrentRequest == null || CurrentRequest.Id != RequestId))
            {
                await LoadReviewRequest();
                await LoadNotesContent();
                await LoadComments();
            }
        }
        #endregion

        #region Data Loading Methods
        private async Task LoadReviewRequest()
        {
            try
            {
                CurrentRequest = await CosigningRequestService.GetByIdAsync(
                    RequestId, 
                    UserContext.ActiveUserOrganizationID, 
                    UserContext.ActiveUserSubscription);

                if (CurrentRequest == null)
                {
                    Logger.LogWarning("Cosigning request {RequestId} not found", RequestId);
                    Snackbar.Add(Localizer["RequestNotFound"], Severity.Warning);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading cosigning request {RequestId}", RequestId);
                throw;
            }
        }

        private async Task LoadNotesContent()
        {
            if (CurrentRequest == null) return;

            try
            {
                IsLoadingNotes = true;
                StateHasChanged();

                // Load the progress notes for the record
                var progressNotes = await ProgressNotesService.GetRecordByIdAsync(
                    CurrentRequest.RecordId,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription);

                NotesContent = new List<TeyaWebApp.Components.Shared.UnifiedNotesDisplay.NoteSection>();

                if (progressNotes != null && !string.IsNullOrWhiteSpace(progressNotes.Notes))
                {
                    // For now, display the notes as a single section
                    // In the future, this could be enhanced to parse structured notes
                    NotesContent.Add(new TeyaWebApp.Components.Shared.UnifiedNotesDisplay.NoteSection
                    {
                        SectionName = "Progress Notes",
                        Content = progressNotes.Notes
                    });

                    // If there's a transcription, add it as a separate section
                    if (!string.IsNullOrWhiteSpace(progressNotes.Transcription))
                    {
                        NotesContent.Add(new TeyaWebApp.Components.Shared.UnifiedNotesDisplay.NoteSection
                        {
                            SectionName = "Transcription",
                            Content = progressNotes.Transcription
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading notes content for record {RecordId}", CurrentRequest.RecordId);
                Snackbar.Add(Localizer["ErrorLoadingNotes"], Severity.Error);
            }
            finally
            {
                IsLoadingNotes = false;
                StateHasChanged();
            }
        }

        private async Task LoadComments()
        {
            if (CurrentRequest == null) return;

            try
            {
                var comments = await CosigningRequestService.GetCommentsAsync(
                    RequestId,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription);
                Comments = comments?.ToList() ?? new List<CosigningComment>();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading comments for request {RequestId}", RequestId);
                Comments = new List<CosigningComment>();
            }
        }
        #endregion

        #region Action Methods
        private async Task ApproveRequest()
        {
            if (CurrentRequest == null) return;

            try
            {
                IsProcessing = true;
                StateHasChanged();

                var reviewerId = Guid.Parse(CurrentUser.id);
                var reviewerName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown Reviewer";

                await CosigningRequestService.ApproveRequestAsync(
                    CurrentRequest.Id,
                    CurrentRequest.ReviewerId,
                    UserContext.ActiveUserOrganizationID,
                    false
                    );

                // Send approval notification - placeholder for future implementation

                Snackbar.Add(Localizer["RequestApprovedSuccessfully"], Severity.Success);
                
                await OnReviewCompleted.InvokeAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error approving request {RequestId}", RequestId);
                Snackbar.Add(Localizer["ErrorApprovingRequest"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }

        private async Task AddComment()
        {
            if (CurrentRequest == null || string.IsNullOrWhiteSpace(NewCommentContent)) return;

            try
            {
                IsProcessing = true;
                StateHasChanged();

                var commenterId = Guid.Parse(CurrentUser.id);
                var commenterName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown Reviewer";

                await CosigningRequestService.AddCommentAsync(
                    CurrentRequest.Id,
                    commenterId,
                    commenterName,
                    NewCommentContent,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription);

                // Send comment notification
                var newComment = new CosigningComment
                {
                    Id = Guid.NewGuid(),
                    CommenterId = commenterId,
                    CommenterName = commenterName,
                    Comment = NewCommentContent,
                    CommentDate = DateTime.UtcNow
                };
                // Send comment notification - placeholder for future implementation

                // Clear the comment editor
                NewCommentContent = string.Empty;
                if (commentEditor != null)
                {
                    await commentEditor.RefreshUIAsync();
                }

                // Reload comments
                await LoadComments();
                
                Snackbar.Add(Localizer["CommentAddedSuccessfully"], Severity.Success);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error adding comment to request {RequestId}", RequestId);
                Snackbar.Add(Localizer["ErrorAddingComment"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }

        private async Task ResolveComment(Guid commentId)
        {
            if (CurrentRequest == null) return;

            try
            {
                IsProcessing = true;
                StateHasChanged();

                var resolverId = Guid.Parse(CurrentUser.id);
                var resolverName = CurrentUser.displayName ?? CurrentUser.givenName ?? "Unknown User";

                await CosigningRequestService.ResolveCommentAsync(
                    CurrentRequest.Id,
                    commentId,
                    resolverId,
                    resolverName,
                    UserContext.ActiveUserOrganizationID,
                    UserContext.ActiveUserSubscription);

                // Reload comments
                await LoadComments();
                
                Snackbar.Add(Localizer["CommentResolvedSuccessfully"], Severity.Success);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error resolving comment {CommentId}", commentId);
                Snackbar.Add(Localizer["ErrorResolvingComment"], Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }

        private void ToggleCommentMode()
        {
            IsCommentMode = !IsCommentMode;
            StateHasChanged();
        }

        private async Task CloseReview()
        {
            await OnReviewClosed.InvokeAsync();
        }
        #endregion

        #region Helper Methods
        private string GetStatusClass(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => "status-pending",
                CosigningRequestStatus.Approved => "status-approved",
                CosigningRequestStatus.ChangesRequested => "status-changes-requested",
                _ => "status-pending"
            };
        }

        private string GetStatusText(CosigningRequestStatus status)
        {
            return status switch
            {
                CosigningRequestStatus.Pending => Localizer["Pending"],
                CosigningRequestStatus.Approved => Localizer["Approved"],
                CosigningRequestStatus.ChangesRequested => Localizer["ChangesRequested"],
                _ => Localizer["Unknown"]
            };
        }

        private bool CanResolveComment(CosigningComment comment)
        {
            // Allow comment resolution by the reviewer or the original commenter
            var currentUserId = Guid.Parse(CurrentUser.id);
            return !comment.IsResolved && 
                   (currentUserId == comment.CommenterId || 
                    currentUserId == CurrentRequest?.ReviewerId);
        }

        private SfRichTextEditor? GetRteReference(string sectionName)
        {
            RteReferences.TryGetValue(sectionName, out var rte);
            return rte;
        }

        private void HandleNoteContentChange((string SectionName, string Content) change)
        {
            var section = NotesContent.FirstOrDefault(n => n.SectionName == change.SectionName);
            if (section != null)
            {
                section.Content = change.Content;
            }
        }

        private void HandleTextSelection((string SectionName, string SelectedText) selection)
        {
            SelectedText = selection.SelectedText;
            // You can use this for context-aware commenting
        }
        #endregion

        #region Toolbar Configuration
        private List<Syncfusion.Blazor.RichTextEditor.ToolbarItemModel> GetReviewToolbarItems()
        {
            var items = new List<Syncfusion.Blazor.RichTextEditor.ToolbarItemModel>();
            
            if (IsCommentMode)
            {
                // Full toolbar for commenting
                items.AddRange(new[]
                {
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Bold },
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Italic },
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Underline },
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.FontColor },
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.BackgroundColor },
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Formats },
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.OrderedList },
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.UnorderedList }
                });
            }
            else
            {
                // Read-only toolbar
                items.AddRange(new[]
                {
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Print },
                    new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.FullScreen }
                });
            }

            return items;
        }

        private List<Syncfusion.Blazor.RichTextEditor.ToolbarItemModel> GetCommentToolbarItems()
        {
            return new List<Syncfusion.Blazor.RichTextEditor.ToolbarItemModel>
            {
                new() { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Bold },
                new() { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Italic },
                new() { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Underline },
                new() { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.FontColor },
                new() { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.OrderedList },
                new() { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.UnorderedList },
                new() { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Undo },
                new() { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Redo }
            };
        }
        #endregion
    }


}
