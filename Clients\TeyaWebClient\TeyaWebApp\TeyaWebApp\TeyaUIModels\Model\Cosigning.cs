using System;

namespace TeyaUIModels.Model
{
    public class Cosigning : IModel
    {
        public Guid Id { get; set; }
        public string CosignerName { get; set; }
        public string SignerName { get; set; }
        public Guid SignerId { get; set; }
        public Guid? CosignerId { get; set; }
        public bool IsSigned { get; set; } = false;
        public bool IsCosigned { get; set; } = false;
        public bool IsLocked { get; set; } = false;
        public Guid RecordId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime? Date { get; set; }
        public DateTime? LastUpdated { get; set; }
        public bool Subscription { get; set; }
        public bool IsDeleted { get; set; } = false;
        public CosigningType Type { get; set; } = CosigningType.SimpleSign;
    }

    public enum CosigningType
    {
        SimpleSign = 0,
        CosignRequest = 1
    }
}