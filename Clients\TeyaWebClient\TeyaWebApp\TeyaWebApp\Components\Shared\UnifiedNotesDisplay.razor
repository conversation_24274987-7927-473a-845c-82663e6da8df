@using TeyaUIModels.Model
@using Syncfusion.Blazor.RichTextEditor
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<style>
    .unified-notes-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: white;
        border-radius: 8px;
        overflow: hidden;
    }

    .notes-section {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        margin-bottom: 16px;
        overflow: hidden;
        transition: all 0.2s ease;
    }

    .notes-section:hover {
        border-color: #1976d2;
        box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
    }

    .section-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 12px 16px;
        border-bottom: 1px solid #e0e0e0;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: background 0.2s ease;
    }

    .section-header:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    }

    .section-header.expanded {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-bottom-color: #1976d2;
    }

    .section-title {
        font-weight: 600;
        color: #333;
        font-size: 0.95rem;
        margin: 0;
    }

    .section-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .content-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #4caf50;
    }

    .content-indicator.empty {
        background: #e0e0e0;
    }

    .expand-icon {
        transition: transform 0.2s ease;
        color: #666;
    }

    .expand-icon.expanded {
        transform: rotate(180deg);
        color: #1976d2;
    }

    .section-content {
        padding: 0;
        background: white;
    }

    .rte-wrapper {
        border: none;
        border-radius: 0;
    }

    .rte-wrapper .e-rte-content {
        min-height: 120px;
        max-height: 400px;
        overflow-y: auto;
        padding: 16px;
        line-height: 1.6;
        font-size: 14px;
    }

    .rte-wrapper .e-toolbar {
        border-bottom: 1px solid #e0e0e0;
        background: #fafafa;
    }

    .comment-mode .rte-wrapper {
        border: 2px solid #2196f3;
        border-radius: 4px;
    }

    .comment-mode .section-header {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-bottom-color: #2196f3;
    }

    .readonly-content {
        padding: 16px;
        line-height: 1.6;
        font-size: 14px;
        min-height: 120px;
        background: #fafafa;
        border-radius: 4px;
        margin: 8px;
    }

    .empty-content {
        padding: 24px;
        text-align: center;
        color: #999;
        font-style: italic;
        background: #f8f9fa;
        border-radius: 4px;
        margin: 8px;
    }

    .section-stats {
        font-size: 0.75rem;
        color: #666;
        margin-left: 8px;
    }

    .comment-overlay {
        position: absolute;
        top: 8px;
        right: 8px;
        background: #2196f3;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        z-index: 10;
    }
</style>

<div class="unified-notes-container @(IsCommentMode ? "comment-mode" : "")">
    @if (NoteSections != null && NoteSections.Any())
    {
        @foreach (var section in NoteSections)
        {
            <div class="notes-section">
                <div class="section-header @(IsExpanded(section.SectionName) ? "expanded" : "")"
                     @onclick="@(() => ToggleSection(section.SectionName))">
                    <div style="display: flex; align-items: center;">
                        <h4 class="section-title">@GetSectionDisplayName(section.SectionName)</h4>
                        <div class="section-stats">
                            @GetContentStats(section.Content)
                        </div>
                    </div>
                    <div class="section-indicator">
                        <div class="content-indicator @(string.IsNullOrWhiteSpace(section.Content) ? "empty" : "")"></div>
                        <MudIcon Icon="@Icons.Material.Filled.ExpandMore"
                                 Class="@($"expand-icon {(IsExpanded(section.SectionName) ? "expanded" : "")}")"
                                 Size="Size.Small" />
                    </div>
                </div>

                @if (IsExpanded(section.SectionName))
                {
                    <div class="section-content">
                        @if (IsCommentMode)
                        {
                            <div class="comment-overlay">
                                @Localizer["CommentMode"]
                            </div>
                        }

                        @if (string.IsNullOrWhiteSpace(section.Content))
                        {
                            <div class="empty-content">
                                <MudIcon Icon="@Icons.Material.Filled.Description" Color="Color.Default" Size="Size.Medium" />
                                <div style="margin-top: 8px;">@Localizer["NoContentAvailable"]</div>
                            </div>
                        }
                        else if (IsReadOnly && !IsCommentMode)
                        {
                            <div class="readonly-content">
                                @((MarkupString)section.Content)
                            </div>
                        }
                        else
                        {
                            <div class="rte-wrapper">
                                <SfRichTextEditor Value="@(section.Content)"
                                                  ValueChanged="@((string newValue) => OnContentChangedInternal(section.SectionName, newValue))"
                                                  Readonly="@(IsReadOnly && !IsCommentMode)"
                                                  Placeholder="@GetPlaceholderText(section.SectionName)">
                                    <RichTextEditorToolbarSettings Items="@GetToolbarItems()" />
                                </SfRichTextEditor>
                            </div>
                        }
                    </div>
                }
            </div>
        }
    }
    else
    {
        <div class="empty-content" style="margin: 0;">
            <MudIcon Icon="@Icons.Material.Filled.Description" Color="Color.Default" Size="Size.Large" />
            <div style="margin-top: 16px;">
                <MudText Typo="Typo.h6">@Localizer["NoNotesAvailable"]</MudText>
                <MudText Typo="Typo.body2" Style="color: #666; margin-top: 8px;">
                    @Localizer["NotesWillAppearHere"]
                </MudText>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public List<NoteSection> NoteSections { get; set; } = new();
    [Parameter] public bool IsReadOnly { get; set; } = true;
    [Parameter] public bool IsCommentMode { get; set; } = false;
    [Parameter] public EventCallback<(string SectionName, string Content)> OnContentChanged { get; set; }
    [Parameter] public EventCallback<(string SectionName, string SelectedText)> OnTextSelected { get; set; }
    [Parameter] public List<string> InitiallyExpanded { get; set; } = new();

    private Dictionary<string, SfRichTextEditor> RteReferences = new();
    private HashSet<string> ExpandedSections = new();
    private Dictionary<string, string> SectionSelections = new();

    protected override void OnInitialized()
    {
        // Initialize expanded sections
        if (InitiallyExpanded.Any())
        {
            ExpandedSections = InitiallyExpanded.ToHashSet();
        }
        else if (NoteSections.Any())
        {
            // Expand first section with content by default
            var firstWithContent = NoteSections.FirstOrDefault(s => !string.IsNullOrWhiteSpace(s.Content));
            if (firstWithContent != null)
            {
                ExpandedSections.Add(firstWithContent.SectionName);
            }
            else
            {
                ExpandedSections.Add(NoteSections.First().SectionName);
            }
        }
    }

    private bool IsExpanded(string sectionName)
    {
        return ExpandedSections.Contains(sectionName);
    }

    private void ToggleSection(string sectionName)
    {
        if (ExpandedSections.Contains(sectionName))
        {
            ExpandedSections.Remove(sectionName);
        }
        else
        {
            ExpandedSections.Add(sectionName);
        }
        StateHasChanged();
    }

    private SfRichTextEditor? GetRteReference(string sectionName)
    {
        RteReferences.TryGetValue(sectionName, out var rte);
        return rte;
    }

    private async Task OnContentChangedInternal(string sectionName, string newContent)
    {
        var section = NoteSections.FirstOrDefault(s => s.SectionName == sectionName);
        if (section != null)
        {
            section.Content = newContent;
            await OnContentChanged.InvokeAsync((sectionName, newContent));
        }
    }

    private async Task OnSelectionChangedInternal(string sectionName, string selectedText)
    {
        if (IsCommentMode && !string.IsNullOrWhiteSpace(selectedText))
        {
            SectionSelections[sectionName] = selectedText;
            await OnTextSelected.InvokeAsync((sectionName, selectedText));
        }
    }

    private string GetSectionDisplayName(string sectionName)
    {
        return sectionName switch
        {
            "ChiefComplaint" => Localizer["ChiefComplaint"],
            "HPI" => Localizer["HistoryOfPresentIllness"],
            "ROS" => Localizer["ReviewOfSystems"],
            "PhysicalExamination" => Localizer["PhysicalExamination"],
            "Assessment" => Localizer["Assessment"],
            "Plan" => Localizer["Plan"],
            "AdditionalNotes" => Localizer["AdditionalNotes"],
            _ => sectionName
        };
    }

    private string GetContentStats(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return Localizer["Empty"];

        var wordCount = content.Split(new[] { ' ', '\n', '\r', '\t' }, StringSplitOptions.RemoveEmptyEntries).Length;
        return $"{wordCount} {(wordCount == 1 ? Localizer["Word"] : Localizer["Words"])}";
    }

    private string GetPlaceholderText(string sectionName)
    {
        return sectionName switch
        {
            "ChiefComplaint" => Localizer["EnterChiefComplaint"],
            "HPI" => Localizer["EnterHistoryOfPresentIllness"],
            "ROS" => Localizer["EnterReviewOfSystems"],
            "PhysicalExamination" => Localizer["EnterPhysicalExamination"],
            "Assessment" => Localizer["EnterAssessment"],
            "Plan" => Localizer["EnterPlan"],
            "AdditionalNotes" => Localizer["EnterAdditionalNotes"],
            _ => Localizer["EnterContent"]
        };
    }

    private List<Syncfusion.Blazor.RichTextEditor.ToolbarItemModel> GetToolbarItems()
    {
        var items = new List<Syncfusion.Blazor.RichTextEditor.ToolbarItemModel>();

        if (IsCommentMode)
        {
            // Full editing toolbar for comment mode
            items.AddRange(new[]
            {
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Bold },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Italic },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Underline },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.StrikeThrough },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Separator },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.FontColor },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.BackgroundColor },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Separator },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.OrderedList },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.UnorderedList },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Separator },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Undo },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Redo }
            });
        }
        else if (!IsReadOnly)
        {
            // Standard editing toolbar
            items.AddRange(new[]
            {
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Bold },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Italic },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Underline },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Separator },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.OrderedList },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.UnorderedList },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Separator },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Undo },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Redo }
            });
        }
        else
        {
            // Read-only toolbar
            items.AddRange(new[]
            {
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.Print },
                new Syncfusion.Blazor.RichTextEditor.ToolbarItemModel { Command = Syncfusion.Blazor.RichTextEditor.ToolbarCommand.FullScreen }
            });
        }

        return items;
    }

    public class NoteSection
    {
        public string SectionName { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
    }
}
