using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Logging;
using System;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using DotNetEnv;

namespace TeyaWebApp.Services
{
    /// <summary>
    /// SignalR client service for receiving cosigning notifications
    /// </summary>
    public class CosigningNotificationService : ICosigningNotificationService, IAsyncDisposable
    {
        private readonly ILogger<CosigningNotificationService> _logger;
        private readonly ITokenService _tokenService;
        private readonly string _encounterNotesURL;
        private HubConnection? _hubConnection;

        public CosigningNotificationService(
            ILogger<CosigningNotificationService> logger,
            ITokenService tokenService)
        {
            _logger = logger;
            _tokenService = tokenService;
            Env.Load();
            _encounterNotesURL = Environment.GetEnvironmentVariable("EncounterNotesURL");
        }

        public bool IsConnected => _hubConnection?.State == HubConnectionState.Connected;

        public event Func<CosigningNotificationData, Task> OnNewCosigningRequest;
        public event Func<CosigningNotificationData, Task> OnRequestApproved;
        public event Func<CosigningNotificationData, Task> OnRequestCommented;
        public event Func<CosigningNotificationData, Task> OnCommentResolved;

        public async Task StartAsync()
        {
            try
            {
                if (_hubConnection != null)
                {
                    await _hubConnection.DisposeAsync();
                }

                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var hubUrl = $"{_encounterNotesURL}/cosigningNotificationHub";

                _hubConnection = new HubConnectionBuilder()
                    .WithUrl(hubUrl, options =>
                    {
                        options.AccessTokenProvider = () => Task.FromResult(accessToken);
                    })
                    .WithAutomaticReconnect()
                    .Build();

                // Register event handlers
                _hubConnection.On<object>("ReceiveCosigningNotification", async (notification) =>
                {
                    try
                    {
                        var jsonString = JsonSerializer.Serialize(notification);
                        var notificationData = JsonSerializer.Deserialize<CosigningNotificationData>(jsonString);

                        if (notificationData != null)
                        {
                            await HandleNotification(notificationData);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing cosigning notification");
                    }
                });

                _hubConnection.Reconnecting += (error) =>
                {
                    _logger.LogWarning("SignalR connection lost. Attempting to reconnect...");
                    return Task.CompletedTask;
                };

                _hubConnection.Reconnected += (connectionId) =>
                {
                    _logger.LogInformation("SignalR connection restored. Connection ID: {ConnectionId}", connectionId);
                    return Task.CompletedTask;
                };

                _hubConnection.Closed += async (error) =>
                {
                    _logger.LogError(error, "SignalR connection closed");
                    await Task.Delay(new Random().Next(0, 5) * 1000);
                    await StartAsync();
                };

                await _hubConnection.StartAsync();
                _logger.LogInformation("SignalR connection started successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting SignalR connection");
            }
        }

        public async Task StopAsync()
        {
            if (_hubConnection != null)
            {
                await _hubConnection.StopAsync();
                await _hubConnection.DisposeAsync();
                _hubConnection = null;
                _logger.LogInformation("SignalR connection stopped");
            }
        }

        private async Task HandleNotification(CosigningNotificationData notification)
        {
            try
            {
                _logger.LogInformation("Received cosigning notification: {Type} - {Message}", notification.Type, notification.Message);

                switch (notification.Type)
                {
                    case "NewCosigningRequest":
                        if (OnNewCosigningRequest != null)
                            await OnNewCosigningRequest.Invoke(notification);
                        break;

                    case "RequestApproved":
                        if (OnRequestApproved != null)
                            await OnRequestApproved.Invoke(notification);
                        break;

                    case "RequestCommented":
                        if (OnRequestCommented != null)
                            await OnRequestCommented.Invoke(notification);
                        break;

                    case "CommentResolved":
                        if (OnCommentResolved != null)
                            await OnCommentResolved.Invoke(notification);
                        break;

                    default:
                        _logger.LogWarning("Unknown notification type: {Type}", notification.Type);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling cosigning notification");
            }
        }

        public async ValueTask DisposeAsync()
        {
            await StopAsync();
        }
    }
}
